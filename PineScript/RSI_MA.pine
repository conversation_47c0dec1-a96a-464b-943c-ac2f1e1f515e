//@version=6
strategy("SMA Cross with RSI Filter Strategy", shorttitle="SMA RSI Filter", overlay=true)

// Define periods for SMAs and RSI
shortSmaPeriod = input.int(6, title="Short SMA Period", minval=1)
longSmaPeriod = input.int(14, title="Long SMA Period", minval=1)
rsiPeriod = input.int(14, title="RSI Period", minval=1)
rsiOverbought = input.int(60, title="RSI Overbought Level", minval=0, maxval=100)
rsiOversold = input.int(40, title="RSI Oversold Level", minval=0, maxval=100)

// Calculate indicators
shortSma = ta.sma(close, shortSmaPeriod)
longSma = ta.sma(close, longSmaPeriod)
rsi = ta.rsi(close, rsiPeriod)

// Plot SMAs
plot(shortSma, color=color.blue, title="Short SMA")
plot(longSma, color=color.red, title="Long SMA")

// Entry conditions
longCondition = ta.crossover(shortSma, longSma) and rsi < rsiOversold
shortCondition = ta.crossunder(shortSma, longSma) and rsi > rsiOverbought

// Strategy entries
if longCondition
    strategy.entry("Long", strategy.long)

if shortCondition
    strategy.entry("Short", strategy.short)

// No explicit exit conditions are defined in the prompt, so positions will remain open until an opposite signal or manual closure.
// You can add exit conditions here if needed. For example:
if ta.crossunder(shortSma, longSma)
    strategy.close("Long")
if ta.crossover(shortSma, longSma)
    strategy.close("Short")