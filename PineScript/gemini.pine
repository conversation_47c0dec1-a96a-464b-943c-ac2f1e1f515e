//@version=5
strategy("Price Action Trend Retracement", shorttitle="PA Retrace", overlay=true)

// --- Inputs ---
trendLookback = input.int(20, "Trend Lookback Period", minval=1)
maLength = input.int(50, "MA Length (for dynamic S/R)", minval=1)
riskRewardRatio = input.float(1.5, "Risk to Reward Ratio", minval=1)
riskPerTradePct = input.float(1.0, "Risk per Trade (%)", minval=0.1, maxval=100)

// --- Trend Identification (using Moving Average) ---
ma = ta.sma(close, maLength)
uptrend = close > ma
downtrend = close < ma

// --- Candlestick Pattern Detection (Simplified Examples) ---
// More complex and specific pattern detection can be added
isHammer() => open > close and (close - low) > 2 * (open - close) and (high - open) < (open - close)
isShootingStar() => open < close and (high - open) > 2 * (close - open) and (close - low) < (high - open)
isBullishEngulfing() => open[1] > close[1] and close > open and close > open[1] and open < close[1]
isBearishEngulfing() => open[1] < close[1] and open > close and open > close[1] and close < open[1]

// --- Entry Conditions ---

longCondition = false
if uptrend and low <= ma // Retracement to MA
    if isHammer() or isBullishEngulfing() // Confirming bullish pattern
        longCondition = true

shortCondition = false
if downtrend and high >= ma // Retracement to MA
    if isShootingStar() or isBearishEngulfing() // Confirming bearish pattern
        shortCondition = true

// --- Risk Management and Order Placement ---

strategy.initial_capital = 10000 // Example initial capital

if longCondition
    stopLossPrice = low - atr(14) * 0.5 // Stop loss slightly below hammer low, adjust multiplier as needed
    if isBullishEngulfing()
        stopLossPrice = low[1] - atr(14) * 0.5 // Stop loss below the low of the engulfing pattern
    
    riskAmount = strategy.initial_capital * (riskPerTradePct / 100)
    // Assuming a fixed stop loss in terms of price difference for simplicity in calculating quantity
    // A more robust position sizing would calculate per-pip risk and use that.
    // For this example, let's assume a simplified quantity calculation based on a notional stop distance.
    // ** NOTE: This is a simplified quantity calculation. For real trading, use a more precise method
    // based on account currency, instrument, and actual stop distance in currency units. **
    notionalStopDistance = close - stopLossPrice
    if notionalStopDistance > 0
        // Simplified: riskAmount / notionalStopDistance would give units if price is in currency per unit.
        // For stocks, this might be close to the number of shares. For forex, it's more complex with pip values.
        // We'll use a placeholder quantity for strategy testing purposes.
        // In a real-world scenario, you'd calculate `strategy.position_size` based on the risk amount and stop distance.
        // For backtesting, we can directly use `strategy.enter` with a calculated quantity.
        // Let's assume a fixed quantity for this basic example, or calculate based on a simplified risk model.
        // A better approach for Pine Script strategy backtesting is to define `strategy.risk.allow_entry_component(strategy.risk.percent_of_equity, riskPerTradePct)`
        // and then use `strategy.enter` without a specified quantity, letting the strategy engine handle it.
        // However, to demonstrate stop loss and take profit calculation based on entry/stop, we'll calculate manually.
        
        // Simplified Quantity Calculation (for demonstration)
        // This is NOT a financially accurate position sizing method.
        // It's a placeholder to allow the strategy to enter trades.
        placeholderQuantity = riskAmount / (notionalStopDistance * syminfo.pointvalue) // syminfo.pointvalue for tick value

        takeProfitPrice = close + notionalStopDistance * riskRewardRatio
        strategy.enter("Long", strategy.long, qty=placeholderQuantity)
        strategy.exit("Exit Long", from_entry="Long", stop=stopLossPrice, limit=takeProfitPrice)

if shortCondition
    stopLossPrice = high + atr(14) * 0.5 // Stop loss slightly above shooting star high, adjust multiplier as needed
    if isBearishEngulfing()
        stopLossPrice = high[1] + atr(14) * 0.5 // Stop loss above the high of the engulfing pattern

    riskAmount = strategy.initial_capital * (riskPerTradePct / 100)
    // Simplified Quantity Calculation (for demonstration)
    notionalStopDistance = stopLossPrice - close
    if notionalStopDistance > 0
        placeholderQuantity = riskAmount / (notionalStopDistance * syminfo.pointvalue) // syminfo.pointvalue for tick value

        takeProfitPrice = close - notionalStopDistance * riskRewardRatio
        strategy.enter("Short", strategy.short, qty=placeholderQuantity)
        strategy.exit("Exit Short", from_entry="Short", stop=stopLossPrice, limit=takeProfitPrice)

// --- Plotting (Optional) ---
plot(ma, color=color.blue, title="Moving Average")
// Plotting signals can be added here for visual confirmation during backtesting
// plotshape(longCondition, style=shape.triangleup, color=color.green, location=location.belowbar, size=size.small)
// plotshape(shortCondition, style=shape.triangledown, color=color.red, location=location.abovebar, size=size.small)