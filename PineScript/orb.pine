//@version=6
strategy('Session Breakout Strategy', overlay = true, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, pyramiding = 0)

// Variables to track session data
var float highestHigh = na
var float lowestLow = na
var int barInSession = 0
var float entryPrice = na
var string tradeType = na

// Calculate bars since session start
if session.isfirstbar
    barInSession := 1
    highestHigh := high
    lowestLow := low
    lowestLow
else
    barInSession := barInSession + 1
    barInSession

// Track high/low for first 4 bars
if barInSession <= 4
    highestHigh := math.max(highestHigh, high)
    lowestLow := math.min(lowestLow, low)
    lowestLow

// Calculate trailing stop parameters
trailPoints = close * 0.02 // 2% of security price
trailOffset = close * 0.004 // 0.4% trailing stop

// Entry conditions (from 5th bar onwards)
longCondition = barInSession > 4 and high > highestHigh and strategy.position_size == 0 // and year >= 2025
shortCondition = barInSession > 4 and low < lowestLow and strategy.position_size == 0 // and year >= 2025

// Execute trades
if longCondition
    entryPrice := close
    tradeType := 'long'
    strategy.entry('Long', strategy.long)
    strategy.exit('Long Exit', 'Long', trail_points = trailPoints / syminfo.mintick, trail_offset = trailOffset / syminfo.mintick)

if shortCondition
    entryPrice := close
    tradeType := 'short'
    strategy.entry('Short', strategy.short)
    strategy.exit('Short Exit', 'Short', trail_points = trailPoints / syminfo.mintick, trail_offset = trailOffset / syminfo.mintick)

// Close all trades on session end
if session.islastbar and strategy.position_size != 0
    strategy.close_all('Session End')

// Plotting
plotshape(longCondition, 'Long Entry', shape.triangleup, location.belowbar, color.green, size = size.small)
plotshape(shortCondition, 'Short Entry', shape.triangledown, location.abovebar, color.red, size = size.small)

// Calculate and display profit
plot(highestHigh, 'Highest High', color.green, 1, plot.style_circles)
plot(lowestLow, 'Lowest Low', color.red, 1, plot.style_circles)

// Display profit on chart when trade is closed
profitLabel = ''
if strategy.closedtrades > 0 and strategy.closedtrades.exit_bar_index(strategy.closedtrades - 1) == bar_index
    profit = strategy.closedtrades.profit(strategy.closedtrades - 1)
    profitLabel := profit > 0 ? '+$' + str.tostring(profit, '#.##') : '-$' + str.tostring(math.abs(profit), '#.##')
    profitLabel

label.new(bar_index, high, profitLabel, color = strategy.closedtrades.profit(strategy.closedtrades - 1) > 0 ? color.green : color.red, style = label.style_label_down, textcolor = color.white, size = size.small)