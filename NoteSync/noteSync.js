function stringToBase64Url(str) {
    const encoder = new TextEncoder();
    const data = encoder.encode(str);
    const base64 = btoa(String.fromCharCode.apply(null, data));
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

function arrayBufferToBase64Url(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    const base64 = btoa(binary);
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

function pemToArrayBuffer(pem) {
    const base64 = pem
        .replace(/-----BEGIN PRIVATE KEY-----/g, '')
        .replace(/-----END PRIVATE KEY-----/g, '')
        .replace(/\s+/g, '');
    try {
        const binaryString = atob(base64);
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes.buffer;
    } catch (e) {
        throw new Error(`Failed to decode Base64 from PEM: ${e.message}`);
    }
}

async function signJWT() {
    // 1. Extract Private Key from JSON
    const serviceAccountJson = JSON.parse(Deno.env.get("GOOGLE_SERVICE_ACCOUNT_JSON"));
    const privateKeyPem = serviceAccountJson.private_key;

    // 2. Define JWT Header (Standard RS256) and payload.
    const header = {
        alg: 'RS256',
        typ: 'JWT',
        kid: serviceAccountJson.private_key_id
    };
    const issuedAt = Math.floor(Date.now() / 1000); // Issued At Time.
    const payload = {
        iss: serviceAccountJson.client_email,
        sub: serviceAccountJson.client_email,
        scope: "https://www.googleapis.com/auth/datastore",
        aud: "https://firestore.googleapis.com/",
        iat: issuedAt,
        exp: issuedAt + 3600
    };
    // 3. Encode Header and Payload (Base64URL)
    const encodedHeader = stringToBase64Url(JSON.stringify(header));
    const encodedPayload = stringToBase64Url(JSON.stringify(payload));
    // 4. Prepare Signing Input
    const signingInput = `${encodedHeader}.${encodedPayload}`;
    const signingInputBytes = new TextEncoder().encode(signingInput);
    try {
        // 5. Prepare the Private Key Buffer (using the extracted PEM)
        const privateKeyBuffer = pemToArrayBuffer(privateKeyPem);
        // 6. Import the Private Key into Web Crypto
        const algorithm = {
            name: 'RSASSA-PKCS1-v1_5',
            hash: 'SHA-256',
        };
        const cryptoKey = await crypto.subtle.importKey(
            'pkcs8',          // Format of the key data (PKCS#8 is standard for these keys)
            privateKeyBuffer, // The key data as an ArrayBuffer
            algorithm,        // Algorithm identifier object
            false,            // Key is not extractable
            ['sign']          // Key usage
        );

        // 7. Sign the Data
        const signatureBuffer = await crypto.subtle.sign(
            algorithm,
            cryptoKey,
            signingInputBytes
        );

        // 8. Encode the Signature (Base64URL)
        const encodedSignature = arrayBufferToBase64Url(signatureBuffer);

        // 9. Assemble the Final JWT
        const jwt = `${signingInput}.${encodedSignature}`;
        return jwt;

    } catch (error) {
        console.error('Error signing JWT with Web Crypto:', error);
    }
}

// Create the JWT for API calls.
const accessToken = await signJWT();

async function syncNotes() {
    const noteArray = [ 'Miscellaneous', 'Trading_And_Investing', 'Important_Webpages', 'Movie_Watch_List', 'Book_Summaries', 'TG_Channels', 'To_Do_List', 'Keywords', 'Book_List', 'Quotes'];
    for (const docName of noteArray) {
        const res = await fetch ('https://1nrp.vercel.app/getNote?REDIS_KEY=' + docName, {
            method: 'GET',
            headers: {
                'Cookie': `session_token=${Deno.env.get("BASIC_AUTH_COOKIE")}`,
                'Content-Type': 'application/json',
            },
        });
        const json = await res.json();
        for (const note of json.result) {
          await addItemToArrayInFirestore(docName, note);
        }
    }
    console.log("Sync Completed Successfully!");
}
syncNotes();

async function deleteNotes() {
    const noteArray = [ 'Miscellaneous', 'Trading_And_Investing', 'Important_Webpages', 'Movie_Watch_List', 'Book_Summaries', 'TG_Channels', 'To_Do_List', 'Keywords', 'Book_List', 'Quotes'];
    for (const docName of noteArray) {
		let fsArray = [];
        const res = await fetch ('https://firestore.googleapis.com/v1/projects/nrp888/databases/(default)/documents/Notes_Collection/' + docName, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
            },
        });
        const firestoreData = await res.json();
		const value = firestoreData.fields.Notes.arrayValue.values;
		for (const arr of value) {
		    fsArray.push(arr.stringValue);
		}
        const response = await fetch ('https://1nrp.vercel.app/getNote?REDIS_KEY=' + docName, {
            headers: {
                'Cookie': `session_token=${Deno.env.get("BASIC_AUTH_COOKIE")}`
            },
        });
        const redisData = await response.json();
        for (const item of fsArray) {
            if( !redisData.result.includes(item) ) {
              await removeItemFromArrayInFirestore(docName, item);
            }
        }

    }
}
deleteNotes();

// IIFE To Fetch And Save TB Links In The File System.
(async () => {
    const response = await fetch('https://1nrp.vercel.app/getLink');
    const data = await response.json();
    const linkArray = data.result;
    Deno.writeTextFileSync( "C:/Users/<USER>/Desktop/Sync_Notes/LinksArray.json", JSON.stringify(linkArray, null, 2) );
})();

async function addItemToArrayInFirestore(Document_Name, Value_To_Add) {
    const commitURL = `https://firestore.googleapis.com/v1/projects/nrp888/databases/(default)/documents:commit`; // Commit endpoint.
    // 0 6 * * 0,3    At 06:00 on Sunday and Wednesday.
    const requestBody = { writes: [
          { transform: { document: `projects/nrp888/databases/(default)/documents/Notes_Collection/${Document_Name}`,
            fieldTransforms: [ { fieldPath: "Notes",
            appendMissingElements: { values: [
            {
            stringValue: Value_To_Add
            } ] } } ] } } ] 
    };
    try {
        const response = await fetch(commitURL, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody),
        });
        const responseData = await response.json();
        if (!response.ok) {
            console.error('Error response from Firestore API:', responseData);
            const errorDetails = responseData.error?.message || JSON.stringify(responseData);
            throw new Error(`Firestore API error: ${response.status} ${response.statusText} - ${errorDetails}`);
        }
        console.log('Successfully added item to array.');
        console.log('Firestore Commit Response:', JSON.stringify(responseData, null, 2));
    } catch (error) {
        console.error('Error adding item to Firestore array:', error);
        throw new Error(`Failed to update Firestore document: ${error.message}`);
    }
}

async function removeItemFromArrayInFirestore(Document_Name, Value_To_Remove) {
    const commitURL = `https://firestore.googleapis.com/v1/projects/nrp888/databases/(default)/documents:commit`; // Commit endpoint
    // 0 6 * * 0,3    At 06:00 on Sunday and Wednesday.
    const requestBody = { writes: [
          { transform: { document: `projects/nrp888/databases/(default)/documents/Notes_Collection/${Document_Name}`,
            fieldTransforms: [ { fieldPath: "Notes",
            removeAllFromArray: { values: [
            {
            stringValue: Value_To_Remove
            } ] } } ] } } ] 
    };
    try {
        const response = await fetch(commitURL, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody),
        });
        const responseData = await response.json();
        if (!response.ok) {
            console.error('Error response from Firestore API:', responseData);
        }
        console.log('Value Removed Successfully:', JSON.stringify(responseData, null, 2));
    } catch (error) {
        console.error('Error removing item from Firestore array:', error);
    }
}