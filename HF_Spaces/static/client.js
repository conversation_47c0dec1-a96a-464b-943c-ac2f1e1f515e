const btn = document.getElementById("gen");
const out = document.getElementById("output");
btn.addEventListener("click", async () => {
  const prompt = document.getElementById("prompt").value;
  const max = Number(document.getElementById("max_tokens").value || 128);
  out.textContent = "Generating…";

  try {
    const res = await fetch("/api/generate", {
      method: "POST",
      headers: {"Content-Type":"application/json"},
      body: JSON.stringify({ prompt, max_new_tokens: max })
    });
    const data = await res.json();
    if (data.error) out.textContent = "Error: " + data.error;
    else out.textContent = data.text;
  } catch (err) {
    out.textContent = "Fetch error: " + err;
  }
});
