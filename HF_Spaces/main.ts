// main.ts
import { oak } from "./deps.ts";
const { Application, Router, send } = oak;

// -- Transformers.js import (Deno via esm.sh) --
/**
 * Note: depending on the transformers.js package naming/version, imports may
 * need adjusting. The blog & releases confirm Deno compatibility for v3+.
 */
import {
  AutoTokenizer,
  AutoModelForCausalLM,
} from "https://esm.sh/@huggingface/transformers@3.0.0";

console.log("Starting server...");

const PORT = Number(Deno.env.get("PORT") || 8080);

// Load model at startup
console.log("Loading tokenizer+model (this may take a while)...");
const MODEL_ID = "Qwen/Qwen3-0.6B"; // Hugging Face repo id

// transformers.js uses top-level await; we do the same
try {
  // tokenizer/model factory methods might be named differently across releases.
  // This is the canonical-sounding v3 API: AutoTokenizer.from_pretrained, etc.
  const tokenizer = await AutoTokenizer.from_pretrained(MODEL_ID);
  const model = await AutoModelForCausalLM.from_pretrained(MODEL_ID);

  console.log("Model loaded successfully.");

  const app = new Application();
  const router = new Router();

  // serve static UI
  router.get("/", async (ctx) => {
    await send(ctx, "index.html", { root: "./static" });
  });
  router.get("/client.js", async (ctx) => {
    await send(ctx, "client.js", { root: "./static" });
  });

  // generation endpoint
  router.post("/api/generate", async (ctx) => {
    const body = await ctx.request.body({ type: "json" }).value;
    const prompt = String(body?.prompt ?? "");
    const max_new_tokens = Number(body?.max_new_tokens ?? 128);

    if (!prompt) {
      ctx.response.status = 400;
      ctx.response.body = { error: "empty prompt" };
      return;
    }

    try {
      // Encode, run generate, decode
      const input_ids = await tokenizer.encode(prompt);
      const output = await model.generate({
        input_ids,
        max_new_tokens,
        // add other options here: temperature, do_sample, etc.
      });

      // Handle the output properly - it should be a tensor with sequences
      const output_ids = output.sequences || output;
      const generated_text = await tokenizer.decode(output_ids, { skip_special_tokens: true });

      ctx.response.headers.set("Content-Type", "application/json");
      ctx.response.body = { text: generated_text };
    } catch (err) {
      console.error("Generation error:", err);
      ctx.response.status = 500;
      ctx.response.body = { error: String(err) };
    }
  });

  app.use(router.routes());
  app.use(router.allowedMethods());

  console.log(`Listening on :${PORT}`);
  await app.listen({ port: PORT });
} catch (err) {
  console.error("Failed to start server:", err);
  console.error("Common causes: model files not accessible, network or unsupported format.");
  Deno.exit(1);
}
