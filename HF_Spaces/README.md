# Qwen3-0.6B on Hugging Face Spaces (Deno + Transformers.js)

This Space runs a Deno server (Oak) and loads `Qwen/Qwen3-0.6B` using Transformers.js (Deno-compatible v3+). Place this repo in a **Hugging Face Space (Docker)**.

## How to use
1. Create a new Space on Hugging Face and choose **Docker**.
2. Push this repo to the Space.
3. The Space Docker image will build and the app will be available on the Space URL.

## Notes / Troubleshooting
- Model weights: Transformers.js may require a model in a transformers.js-ready (ONNX / converted) format on HF or an ONNX build. If HF model repo has multiple formats, transformers.js will pick appropriate format or you will need to pre-convert. See Transformers.js docs. :contentReference[oaicite:4]{index=4}
- Resource usage: Qwen3‑0.6B is relatively small but still requires memory/compute. Consider quantized/ONNX versions or using HF-hosted inference if the Space does not have sufficient RAM/compute.
