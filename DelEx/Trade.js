// Do not use Console Logs in production as the market is always open and number of logs will become too much.

import { 
  GetQuotes, GetRanges, SendToTelegram, GetLotSizes,
  PlaceOrder, GetAccountBalance,
  // CreateHmac, CreateHash, Socket, GetPositions, GetOHLC 
} from './FunctionExports.js'

// Delta Exchange API Management Page: https://demo.delta.exchange/app/account/manageapikeys

const INIT_PCT_UP = 1.010, INIT_PCT_DOWN = 0.990, FINAL_PCT_UP = 1.005, FINAL_PCT_DOWN = 0.995 // Stop Loss Percentages.

let EnteredTrade = false, LoopCount = 0, AchievedGoodRR = false

async function CheckBreakout() {
  const Creds = JSON.parse( Deno.readTextFileSync( "./Creds.json" ) )
  const coins = Creds.Assets.map( (cred) => cred.Symbol ).join(',')
  const tickers = await GetQuotes( coins )
  for (const ticker of tickers) {
    const { symbol: Symbol, close: Close, quotes: { best_bid: Bid, best_ask: Ask } } = ticker
    const CurrentPrice = Close // +( (( Number(Bid) + Number(Ask) ) / 2 ).toFixed(4) ) // Use this line if you want to use the average of bid and ask price.
    // console.log('Difference in Price Of Close To Optimal Price:', +( Math.abs(Close - CurrentPrice).toFixed(4)) )
    const { High, Low } = Creds.Assets.find( (cred) => cred.Symbol === Symbol ).Details

    const BreakoutReached = CurrentPrice > High || CurrentPrice < Low
    if (BreakoutReached) {
      EnteredTrade = true
      const LONG = CurrentPrice > High
      Creds.TradeType = LONG ? "LONG" : "SHORT"
      Creds.TradedAsset = Symbol
      Creds.Quantity = Math.floor( Creds.AvailableFunds * 9 /* 10X - 1X(Buffer) Leverage */ / (CurrentPrice * Creds.LotSizes[Symbol]) )
      const { success: Success, result: { average_fill_price: EntryPrice, paid_commission: Commission } } = await PlaceOrder({ Symbol, Quantity: Creds.Quantity, TradeType: LONG ? 'buy' : 'sell', SquareOff_Only: false })
      if (Success) {
        console.log('EntryPrice:', +EntryPrice, 'Commission:', +Commission)
        Creds.EntryPrice = EntryPrice
        Creds.StopLoss = LONG ? Number((EntryPrice * INIT_PCT_DOWN).toFixed(4)) : +((CurrentPrice * INIT_PCT_UP).toFixed(4))
        Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
        console.log(`${Creds.TradeType} Entry For ${Creds.Quantity} Quantity Of ${Symbol} At ${CurrentPrice} With Stop Loss At ${Creds.StopLoss}.`)
        const message = ` <b> ${ Creds.TradeType == "LONG" ? '🟦' : '🟥' } DELTA ENTRY - ${Creds.TradeType} </b>
        <b> Coin: <i> ${Symbol} </i> </b>
        <b> Price: </b> ${CurrentPrice}
        <b> Quantity: </b> ${Creds.Quantity}
        <b> Stop Loss: </b> ${Creds.StopLoss}
        `;
        await SendToTelegram(message)
        break
      } else {
        console.log('⚠️ An Error Occurred While Placing Order:', response)
        break
      }
    }
  }
  console.log('Executed " CheckBreakout() " Function Loop ', ++LoopCount, ' Times...') // To see no. of times in console while testing. Don't use this line in production because the market is always open and no. of logs will become too much.
}

async function CheckStoploss() {
    const Creds = JSON.parse( Deno.readTextFileSync( "./Creds.json" ) )
    const ticker = await GetQuotes( Creds.TradedAsset )
    const { close: Close, quotes: { best_bid: Bid, best_ask: Ask } } = ticker
    const CurrentPrice = Close // +( (( Number(Bid) + Number(Ask) ) / 2 ).toFixed(4) )
    console.log('Distance From StopLoss:', +( Math.abs(Creds.StopLoss - CurrentPrice).toFixed(4) ) )
    const StopLossReached = Creds.TradeType === "LONG" && CurrentPrice < Creds.StopLoss || Creds.TradeType === "SHORT" && CurrentPrice > Creds.StopLoss
    if (StopLossReached) {
      EnteredTrade = false
      const LONG = Creds.TradeType == "LONG"
      AchievedGoodRR = false
      Creds.TradeCount += 1
      const OldBalance = Creds.AvailableFunds, NewBalance = +( ( await GetAccountBalance() ).slice(0, 6) ), PnL = NewBalance - OldBalance
      await PlaceOrder({ Symbol: Creds.TradedAsset, Quantity: Creds.Quantity, TradeType: LONG ? 'sell' : 'buy', SquareOff_Only: true })
      Creds.AvailableFunds = NewBalance
      Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
      console.log(`⚠️ Exit for ${Creds.TradedAsset} at ${CurrentPrice} With P&L : $ ${PnL.toFixed(2)}`)
      
      const message = `<b> 🟨 DELTA EXIT > ${ PnL >= 0 ? " 🟢 PROFIT" : " 🔴 LOSS" } </b>
      <b> Coin: <i> ${Creds.TradedAsset} </i> </b>
      <b> Exit Price: </b> ${CurrentPrice}
      <b> P&L Amount: </b> $ ${PnL.toFixed(2)}
      <b> P&L %: </b> ${ (( PnL / OldBalance ) * 100).toFixed(2) } %
      <b> Trade Type: </b> ${Creds.TradeType}
      `;
      await SendToTelegram(message)
      await GetRanges() // Update the Price Ranges for different coins before scanning for new breakouts.

    } else {
      const Entry = Creds.EntryPrice , Stop = Creds.StopLoss
      // Only evaluate AchievedGoodRR if it's not already true.
      // It is coded this way to prevent making the stop loss 1% again if difference between entry and current price becomes less than difference between entry and stop loss, going forward i.e. nearing Stop Loss.
      if (!AchievedGoodRR) AchievedGoodRR = Math.abs(CurrentPrice - Entry) >= Math.abs(Entry - Stop)
      
      if (Creds.TradeType === "LONG") {
          const NewStopLevel = AchievedGoodRR ? CurrentPrice * FINAL_PCT_DOWN : CurrentPrice * INIT_PCT_DOWN;
          Creds.StopLoss = NewStopLevel > Creds.StopLoss
              ? +(NewStopLevel.toFixed(4))
              : +Creds.StopLoss
      } else {
          const NewStopLevel = AchievedGoodRR ? CurrentPrice * FINAL_PCT_UP : CurrentPrice * INIT_PCT_UP;
          Creds.StopLoss = NewStopLevel < Creds.StopLoss
              ? +(NewStopLevel.toFixed(4))
              : +Creds.StopLoss
      }
      Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
    }
}

async function MainScript() {
  const time = new Date(Date.now())
  const day = time.getDate(), hour = time.getHours()
  
  // Exit the function and give the VPS a rest for 4 hours on Sundays.
  // At 10 AM on each Sunday , the Batch (.bat) file code will again activate the script which will continue till next Sunday.
  if ( day === 0 && hour === 6 ) {
    clearInterval(IntervalID)
    console.log('Closing Script Execution. Exiting...')
    return
  }

  if ( !EnteredTrade ) {
    await CheckBreakout()
  }
  if ( EnteredTrade ) {
    await CheckStoploss()
  }

}

await GetRanges() // Get the Price Ranges for different coins before starting the script.
await GetLotSizes() // Get the Lot Sizes for different Contracts before starting the script.

const IntervalID = setInterval(MainScript, 5000)
// deno task run
