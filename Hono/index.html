<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta charset="utf-8">
  <meta name="theme-color" content="#0d1117"> <!-- Dark GitHub theme color -->
  <link rel="icon" sizes="192x192" href="https://1nrp.github.io/1/Images/N-Logo1.png">
  <title>Cloud Notes</title>
  <style>
    :root {
      --bg-color: #0d1117;
      --text-color: #c9d1d9;
      --border-color: #30363d;
      --input-bg: #161b22;
      --button-bg: #238636;
      --button-hover-bg: #2ea043;
      --button-text: #ffffff;
      --delete-bg: #da3633;
      --delete-hover-bg: #f85149;
      --view-bg: #8957e5;
      --view-hover-bg: #a079f0;
      --clear-bg: #b05d2a;
      --clear-hover-bg: #c97644;
      --note-bg: #21262d;
      --note-border-hover: #58a6ff;
      --select-bg: #161b22;
      --select-border: #30363d;
      --header-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      --body-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      --alert-success-bg: #2ea043;
      --alert-error-bg: #da3633;
      --alert-warn-bg: #e8af05;
      --alert-text: #ffffff;
    }

    body {
      color: var(--text-color);
      background-color: var(--bg-color);
      font-family: var(--body-font);
      margin: 10px;
      padding: 0;
    }

    input:focus,
    textarea:focus,
    select:focus {
      outline: 2px solid var(--note-border-hover); 
      box-shadow: 0 0 5px var(--note-border-hover);
    }

    h2 {
      font-family: var(--header-font);
      font-size: 25px;
      font-weight: 600; /* Adjusted weight */
      margin: 0 0 10px 0; /* Adjusted margin */
      color: var(--text-color);
      animation: fadeIn 0.5s ease-in-out;
    }

    #TopBar {
      display: flex;
      justify-content: space-between; /* Better alignment */
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    #Stores {
      padding: 5px 10px; /* Increased padding */
      border: 1px solid var(--select-border);
      border-radius: 6px; /* Standard border radius */
      color: var(--text-color);
      background-color: var(--select-bg);
      font-weight: 500;
      cursor: pointer;
      transition: border-color 0.2s ease;
    }
    #Stores:hover {
        border-color: var(--note-border-hover);
    }

    #textBox {
      width: 100%; /* Use full width */
      height: 70vh; /* Adjusted height */
      padding: 10px;
      font-size: 15px;
      resize: none;
      overflow: auto;
      font-family: var(--body-font);
      white-space: pre-wrap;
      word-wrap: break-word;
      background-color: var(--note-bg); /* Default note background */
      border: 1px solid var(--border-color);
      color: var(--text-color);
      border-radius: 6px;
      box-sizing: border-box; /* Include padding/border in width/height */
      -webkit-user-modify: read-write;
      -moz-user-modify: read-write;
      margin-bottom: 10px;
    }
    
    .note-entry {
        position: relative; /* Needed for date tooltip positioning */
        background-color: var(--note-bg);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 8px 12px;
        margin-bottom: 8px;
        color: var(--text-color);
        font-family: var(--body-font);
        font-size: 16px;
        white-space: pre-wrap;
        word-wrap: break-word;
        cursor: pointer; /* Indicate it's clickable */
        transition: background-color 0.2s ease, border-color 0.2s ease;
        animation: slideInUp 0.4s ease-out;
    }

    .note-entry:hover {
        background-color: #2a3038; /* Slightly lighter on hover */
        border-color: #484f58;
    }

    .note-entry:hover::before { /* Date tooltip */
        content: attr(data-date);
        position: absolute;
        top: -25px; /* Position above the div */
        left: 10px;
        background-color: #c5740a;
        color: #bacc55;
        padding: 3px 6px;
        border-radius: 4px;
        font-size: 16px;
        border-radius: 4px;
        font-weight: 700;
        white-space: nowrap;
        z-index: 10;
        opacity: 0;
        animation: fadeInTooltip 0.3s forwards;
    }
    
    .note-entry:focus {
        outline: none;
        border: 1px solid var(--note-border-hover);
        box-shadow: 0 0 5px rgba(88, 166, 255, 0.5);
        background-color: #2a3038; 
    }

    #deleteBox {
      color: var(--text-color);
      height: auto;
      min-height: 50px; /* Reduced min-height */
      resize: vertical; /* Allow vertical resize */
      overflow: auto;
      box-sizing: border-box;
      width: 100%;
      background-color: var(--input-bg);
      border: 1px solid var(--border-color);
      border-radius: 6px;
      padding: 8px;
      margin-bottom: 10px;
      font-family: var(--body-font);
    }
    
    .button-container {
        display: flex;
        gap: 10px; /* Space between buttons */
        flex-wrap: wrap; /* Allow buttons to wrap on small screens */
        margin-bottom: 10px;
    }

    button {
        color: var(--button-text);
        border: none;
        font-size: 14px; /* Slightly smaller font */
        border-radius: 6px;
        cursor: pointer;
        padding: 8px 16px; /* Standard padding */
        font-weight: 500;
        transition: background-color 0.2s ease, transform 0.1s ease;
        animation: popIn 0.3s ease-out;
    }
    button:active {
        transform: scale(0.98);
    }

    #deleteBtn {
        background-color: var(--delete-bg);
    }
    #deleteBtn:hover {
        background-color: var(--delete-hover-bg);
    }

    #saveButton {
        background-color: var(--button-bg);
    }
    #saveButton:hover {
        background-color: var(--button-hover-bg);
    }

    #getNotes {
        background-Color: var(--view-bg);
    }
    #getNotes:hover {
        background-color: var(--view-hover-bg);
    }

    #clearBtn {
        background-color: var(--clear-bg);
        padding: 6px 12px; /* Adjusted padding for icon */
    }
    #clearBtn:hover {
        background-color: var(--clear-hover-bg);
    }

    .Alerts {
      position: fixed;
      bottom: 20px; /* Position at the bottom */
      left: 50%;
      transform: translateX(-50%); /* Center horizontally */
      background-color: var(--alert-success-bg); /* Default to success */
      color: var(--alert-text);
      font-weight: 600; /* Bolder */
      padding: 10px 20px; /* More padding */
      border: none;
      border-radius: 6px;
      animation: alertSlideUp 0.5s ease-out, alertFadeOut 0.5s 2.5s forwards; /* Slide up, then fade out */
      z-index: 1000;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }
    
    /* Specific Alert Colors */
    .Alerts.success { background-color: var(--alert-success-bg); }
    .Alerts.error { background-color: var(--alert-error-bg); }
    .Alerts.warning { background-color: var(--alert-warn-bg); }

    /* Keyframe Animations */
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes slideInUp {
      from { transform: translateY(20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }

    @keyframes popIn {
      from { transform: scale(0.8); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    @keyframes fadeInTooltip {
        from { opacity: 0; transform: translateY(5px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @keyframes alertSlideUp {
      from { transform: translate(-50%, 50px); opacity: 0; }
      to { transform: translate(-50%, 0); opacity: 1; }
    }

    @keyframes alertFadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
    
  </style>
</head>
<body>
  <!-- * Keyboard Shortcuts: Fetch Notes: "ArrowRight + ArrowDown", Save Notes: "ArrowRight + ArrowUp", Delete Notes: "ArrowRight + End"  -->
  <div id="TopBar">
    <h2 id="Header">Nihar's Cloud Notes</h2>
    <select id="Stores">
      <option value="Miscellaneous" data-Name="Nihar's Cloud Notes" data-Colour="#21262d">Miscellaneous</option>
      <option value="Trading_And_Investing" data-Name="Trading & Investing" data-Colour="#2a202c">Trading & Investing</option>
      <option value="Important_Webpages" data-Name="Important Webpages" data-Colour="#1c3323">Important Webpages</option>
      <option value="Movie_Watch_List" data-Name="Movie Watch List" data-Colour="#2b1f31">Movie Watch List</option>
      <option value="Book_Summaries" data-Name="Book Summaries" data-Colour="#36251e">Book Summaries</option>
      <option value="TG_Channels" data-Name="TG Channels" data-Colour="#1e3633">TG Channels</option>
      <option value="To_Do_List" data-Name="To Do List" data-Colour="#1e282f">To Do List</option>
      <option value="Keywords" data-Name="Keywords" data-Colour="#161b22">Keywords</option> 
      <option value="Book_List" data-Name="Book List" data-Colour="#3d351d">Book List</option>
      <option value="Quotes" data-Name="Quotes" data-Colour="#142621">Quotes</option>
    </select>
  </div>

  <!-- Container for notes fetched from server -->
  <div id="notesContainer" spellcheck="false" autocomplete="off" translate="no"></div>

  <!-- Text area for composing new notes -->
  <textarea id="textBox" spellcheck="false" autocomplete="off" translate="no" placeholder="Enter your new note here..."></textarea>

  <div class="button-container">
    <button id="saveButton" onclick="saveNotes()">SAVE</button>
    <button id="getNotes" onclick="getNote()">VIEW NOTES</button>
  </div>

  <!-- Text area for specifying notes to delete -->
  <textarea id="deleteBox" spellcheck="false" autocomplete="off" translate="no" placeholder="Click a note above to select it for deletion, or paste the note text here..."></textarea>

  <div class="button-container">
    <button id="deleteBtn" onclick="deleteNote()">DELETE Selected Note</button>
    <button id="clearBtn" onclick="document.getElementById('textBox').value=''">Clear New Note Area</button>
  </div>

<script>
  const notesContainer = document.getElementById('notesContainer');
  const textBox = document.getElementById('textBox'); // For new notes
  const deleteBox = document.getElementById('deleteBox');
  const storesSelect = document.getElementById('Stores');
  const header = document.getElementById('Header');

  // --- Event Listeners ---

  storesSelect.addEventListener('change', function() {
    const dropdown = storesSelect;
    const chosenStore = dropdown.options[dropdown.selectedIndex];
    const Name = chosenStore.getAttribute('data-Name');
    const Colour = chosenStore.getAttribute('data-Colour'); 
    // We'll use data-Colour to set background of the main text box later if needed,
    // but the overall theme is now dark.
    // document.getElementById('textBox').style.backgroundColor = Colour; 
    header.innerText = Name;
    getNote(); // Fetch notes for the newly selected store
  });

  // Auto expand deleteBox
  deleteBox.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
  });
  // Trigger on load if content exists
  document.addEventListener('DOMContentLoaded', () => { 
      deleteBox.dispatchEvent(new Event('input'));
      getNote(); // Fetch notes for the default store on load
  }); 

  // Click on a note in the container to populate the delete box
  notesContainer.addEventListener('click', (event) => {
    if (event.target.classList.contains('note-entry')) {
        const fullText = event.target.getAttribute('data-date') + '' + event.target.textContent;
        deleteBox.value = fullText;
        deleteBox.dispatchEvent(new Event('input')); // Adjust height
        event.target.focus(); // Optional: visually indicate selection
    }
  });

  // --- Functions ---

  // Show Alerts
  function showAlert({ type = 'success', Text = 'Alert' } = {}) {
    var alertBox = document.createElement('div');
    alertBox.className = `Alerts ${type}`; // Add type class for styling
    alertBox.textContent = Text;
    document.body.appendChild(alertBox);
    // Animation handles fade out, but let's ensure removal
    setTimeout(() => { 
        alertBox.remove(); 
    }, 3000); // Matches animation + fade duration
  }

  // Save Note
  async function saveNotes() {
    const AlertText = header.innerText;
    const date = `● Date: ${new Date().toLocaleDateString('en-GB')} ●`;
    const Sentence = textBox.value.trim(); // Use value for textarea
    const lastSentence = date + "" + Sentence;
    if (Sentence) { // Only need Sentence to be non-empty
      try {
        const REDIS_KEY = storesSelect.value;
        const response = await fetch(`https://1nrp.vercel.app/saveNote?REDIS_KEY=${REDIS_KEY}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ lastSentence, REDIS_KEY })
        });
        const data = await response.json();
        if (response.ok) {
          console.log('Note sent to Vercel KV store with KEY:', REDIS_KEY);
          showAlert({ type: 'success', Text: `${data.result} ✔ ${AlertText}` });
          textBox.value = ''; // Clear the input box after saving
          getNote(); // Refresh notes list
        } else {
          console.error('Error from server:', data.error);
          showAlert({ type: 'error', Text: `Save Failed: ${data.error || 'Unknown error'}` });
        }
      } catch (error) {
        console.error('Error sending note to Vercel KV:', error);
        showAlert({ type: 'error', Text: `Network Error: ${error.message}` });
      }
    }
  }

  // Delete Note
  async function deleteNote() {
    const deleteValue = deleteBox.value.trim();
    const AlertText = header.innerText;
    if (deleteValue) {
      try {
        const Redis_Key = storesSelect.value;
        const response = await fetch(`/deleteNote?REDIS_KEY=${Redis_Key}`, {
          method: 'POST',
          headers: { 'Content-Type': 'text/plain' },
          body: deleteValue
        });
        const data = await response.json(); 
        if (response.ok) {
          if (data.result !== 0) {
            console.log('Note deleted in Vercel KV store with KEY:', Redis_Key);
            showAlert({ type: 'error', Text: `${data.result} ✖ ${AlertText} Deleted` }); // Use error style for deletion
            deleteBox.value = ''; // Clear delete box
            deleteBox.dispatchEvent(new Event('input')); // Adjust height
            getNote(); // Refresh notes list
          } else {
            console.log('This Note does not exist in Vercel KV store with KEY:', Redis_Key);
            showAlert({ type: 'warning', Text: `⚠ Note Not Found in ${AlertText}` });
          }
        } else {
          console.error('Error from server:', data.error);
          showAlert({ type: 'error', Text: `Delete Failed: ${data.error || 'Unknown error'}` });
        }
      } catch (error) {
        console.error('Error deleting note in Vercel KV:', error);
        showAlert({ type: 'error', Text: `Network Error: ${error.message}` });
      }
    }
  }

  // Retrieve Notes
  async function getNote() {
    notesContainer.innerHTML = '<p><b>Loading notes...</b></p>'; // Indicate loading
    try {
      const Redis_Key = storesSelect.value;
      // ***** IMPORTANT: Make sure the '/data' endpoint fetches based on the Redis_Key *****
      // You might need to change this fetch URL if '/data' doesn't automatically 
      // use the selected store. Example: `/data?REDIS_KEY=${Redis_Key}`
      const response = await fetch('/data'); // Adjust URL if needed
      if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      notesContainer.innerHTML = ''; // Clear loading message

      if (data && data.length > 0) {
          for (const text of data) {
              const parts = text.split('\n');
              const date = parts.shift() || 'No Date'; // Extract date, provide default
              const note = parts.join('\n'); // Join remaining parts as the note

              const noteDiv = document.createElement('div');
              noteDiv.className = 'note-entry';
              noteDiv.textContent = note;
              noteDiv.setAttribute('data-date', date); // Store date for hover tooltip
              noteDiv.setAttribute('tabindex', '0'); // Make it focusable for keyboard/styling
              
              notesContainer.appendChild(noteDiv);
          }
      } else {
          notesContainer.innerHTML = '<p>No notes found in this category.</p>'; // Message if empty
      }
    } catch (error) {
      console.error('Error fetching or parsing data:', error);
      notesContainer.innerHTML = `<p style="color: var(--delete-bg);">Error loading notes: ${error.message}</p>`; // Show error in container
    }
  }

  // --- Keyboard Shortcuts ---
  let arrowRightPressed = false;
  document.addEventListener("keydown", function(event) {
    // Allow default behavior if inside text areas
    if (event.target === textBox || event.target === deleteBox) {
        if (event.key === "ArrowRight" || event.key === "ArrowUp" || event.key === "ArrowDown" || event.key === "End") {
           // Don't block navigation keys within textareas
           return; 
        }
    }
      
    if (event.key === "ArrowRight") {
      arrowRightPressed = true;
    }

    if (arrowRightPressed) {
      if (event.key === "ArrowUp") {
        event.preventDefault(); // Prevent page scroll
        saveNotes();
      } else if (event.key === "ArrowDown") {
        event.preventDefault(); // Prevent page scroll
        getNote();
      } else if (event.key === "End") {
        event.preventDefault(); // Prevent default End key behavior
        deleteNote();
      }
    }
  });

  document.addEventListener("keyup", function(event) {
    if (event.key === "ArrowRight") {
      arrowRightPressed = false;
    }
  });

</script>
</body>
</html>