<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" sizes="192x192" href="https://1nrp.github.io/1/Images/N-Logo1.png">
  <title>Nihar's JS Notebook</title>
  <style>
    :root {
      --bg-color: #1e1e1e; /* Dark background */
      --cell-bg: #252526; /* Slightly lighter cell background */
      --toolbar-bg: #333333;
      --border-color: #444444;
      --text-color: #d4d4d4; /* Light grey text */
      --accent-color: #4e94ce; /* Blue accent */
      --button-bg: #3a3d41;
      --button-hover-bg: #4a4d51;
      --button-text: var(--text-color);
      --input-bg: #3c3c3c;
      --output-bg: #2a2a2b;
      --log-text: #cccccc;
      --error-text: #f48771;
      --error-bg: rgba(244, 135, 113, 0.1);
      --success-text: #8cc85f;
      --success-bg: rgba(140, 200, 95, 0.1);
      --monospace-font: 'Courier New', Courier, monospace;
      --ui-font: Arial, sans-serif;
      --border-radius: 4px;
      --animation-speed: 0.3s;
    }

    body {
      font-family: var(--ui-font);
      margin: 0;
      background-color: var(--bg-color);
      color: var(--text-color);
      padding: 0;
    }

    input:focus,
    textarea:focus,
    select:focus {
      outline: 1px solid var(--accent-color);
      box-shadow: 0 0 3px var(--accent-color);
    }

    .toolbar {
      padding: 8px 12px;
      background-color: var(--toolbar-bg);
      border-bottom: 1px solid var(--border-color);
      display: flex;
      align-items: center;
      gap: 10px; /* Space between elements */
      flex-wrap: wrap; /* Allow wrapping on smaller screens */
    }

    .toolbar input[type="text"] {
      font-size: 16px;
      border: 1px solid var(--border-color);
      background-color: var(--input-bg);
      color: var(--text-color);
      border-radius: var(--border-radius);
      padding: 6px 10px;
      flex-grow: 1; /* Allow title input to take available space */
      min-width: 150px;
    }
    
    .toolbar .button-group {
        display: flex;
        gap: 5px;
    }

    .toolbar button, .cell-button {
      padding: 6px 12px;
      font-size: 13px;
      font-weight: 500;
      border-radius: var(--border-radius);
      background-color: var(--button-bg);
      color: var(--button-text);
      border: 1px solid var(--border-color);
      cursor: pointer;
      transition: background-color var(--animation-speed) ease, transform 0.1s ease;
      white-space: nowrap; /* Prevent buttons breaking line */
    }

    .toolbar button:hover, .cell-button:hover {
      background-color: var(--button-hover-bg);
    }
    .toolbar button:active, .cell-button:active {
        transform: scale(0.97);
    }

    #upload-notebook { /* Hide the actual file input */
      display: none;
    }

    #notebook {
        padding: 10px;
    }

    .cell {
      border: 1px solid var(--border-color);
      margin-bottom: 12px; /* Space between cells */
      border-radius: var(--border-radius);
      background-color: var(--cell-bg);
      position: relative; /* For positioning buttons */
      overflow: hidden; /* Contain children */
      animation: fadeIn var(--animation-speed) ease-out;
    }
    
    .cell-content {
        padding: 10px; /* Padding inside the cell, around editor/output */
    }

    .code-editor, .markdown-editor {
      width: 100%;
      border-radius: var(--border-radius);
      resize: vertical; /* Allow vertical resize */
      color: var(--text-color);
      background-color: var(--input-bg);
      border: 1px solid var(--border-color);
      font-family: var(--monospace-font);
      font-size: 14px;
      line-height: 1.5;
      padding: 8px;
      box-sizing: border-box; /* Include padding in width */
      min-height: 60px; /* Minimum height */
      display: block; /* Ensure it takes block layout */
      margin-bottom: 5px; /* Space before output/render */
    }
    
    .code-editor {
      overflow-y: hidden; /* Managed by adjustTextareaHeight */
    }
    
    .text-cell .markdown-editor {
      min-height: 80px;
    }


    .cell-buttons {
      position: absolute;
      top: 5px;
      right: 8px;
      display: flex;
      gap: 6px;
      z-index: 10;
      opacity: 0; /* Initially hidden */
      transition: opacity var(--animation-speed) ease;
      background-color: rgba(37, 37, 38, 0.8); /* Semi-transparent background */
      padding: 4px;
      border-radius: var(--border-radius);
    }

    .cell:hover .cell-buttons {
      opacity: 1; /* Show on hover */
    }

    .cell-button {
        padding: 3px 6px; /* Smaller padding for cell buttons */
        font-size: 12px;
    }
    
    .output {
      margin-top: 8px;
      padding: 8px;
      border-top: 1px dashed var(--border-color);
      background-color: var(--output-bg);
      border-radius: 0 0 var(--border-radius) var(--border-radius); /* Rounded bottom corners */
      font-family: var(--monospace-font);
      font-size: 13px;
      overflow-wrap: break-word;
      word-wrap: break-word;
      white-space: pre-wrap;
    }

    .logDiv, .errorDiv {
      padding: 4px 6px;
      margin: 2px 0;
      border-radius: 3px;
      line-height: 1.4;
    }

    .logDiv {
      color: var(--log-text);
      background-color: transparent; /* Inherit from output */
    }

    .errorDiv {
      color: var(--error-text);
      background-color: var(--error-bg);
      border-left: 3px solid var(--error-text);
    }

    .markdown-render {
      padding: 10px;
      background: var(--output-bg); /* Consistent output background */
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      color: var(--text-color); /* Ensure text is readable */
      margin-top: 5px;
      line-height: 1.6;
      overflow-wrap: break-word;
    }
    /* Basic Markdown styling */
    .markdown-render h1, .markdown-render h2, .markdown-render h3 {
        margin-top: 0.8em;
        margin-bottom: 0.4em;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 0.2em;
    }
    .markdown-render code {
        background-color: var(--input-bg);
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-family: var(--monospace-font);
    }
    .markdown-render pre {
        background-color: var(--input-bg);
        padding: 8px;
        border-radius: var(--border-radius);
        overflow-x: auto; /* Allow horizontal scrolling for code blocks */
    }
    .markdown-render blockquote {
        border-left: 3px solid var(--accent-color);
        padding-left: 10px;
        color: #a0a0a0; /* Slightly dimmer text for quotes */
        margin-left: 0;
    }
    .markdown-render a {
        color: var(--accent-color);
        text-decoration: none;
    }
    .markdown-render a:hover {
        text-decoration: underline;
    }


    .minimized > .cell-content > .code-editor,
    .minimized > .cell-content > .output,
    .minimized > .cell-content > .markdown-editor,
    .minimized > .cell-content > .markdown-render {
      display: none; /* Hide content when minimized */
    }
    .minimized {
       min-height: 30px; /* Adjust height when minimized */
       padding-top: 5px;
       padding-bottom: 5px;
    }
    
    .Alerts {
      position: fixed;
      bottom: 20px; /* Position at the bottom */
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      background-color: var(--button-bg); /* Use button bg as default */
      color: var(--button-text);
      font-weight: 600;
      padding: 10px 20px;
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
      animation: alertSlideUpFade var(--animation-speed) ease-out, alertFadeOut var(--animation-speed) 2.5s forwards;
    }
    .Alerts.success {
        background-color: var(--success-bg);
        color: var(--success-text);
        border-color: var(--success-text);
    }
    .Alerts.error {
        background-color: var(--error-bg);
        color: var(--error-text);
        border-color: var(--error-text);
    }


    /* Animations */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes alertSlideUpFade {
      from { transform: translate(-50%, 30px); opacity: 0; }
      to { transform: translate(-50%, 0); opacity: 1; }
    }

    @keyframes alertFadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }

</style>
</head>
<body>
  <div class="toolbar">
    <input id="notebook-title" placeholder="Untitled Notebook" value="Untitled Notebook" type="text">
    <div class="button-group">
      <button id="add-code">+ Code</button>
      <button id="add-text">+ Text</button>
    </div>
    <div class="button-group">
      <button id="download-notebook">Save JSON</button>
      <input id="upload-notebook" type="file" accept=".json">
      <button id="upload-trigger">Load JSON</button>
    </div>
     <div class="button-group">
      <button id="save-local" onclick="setLocal()">Save Local</button>
      <button id="load-local" onclick="getLocal()">Load Local</button>
      <button id="list-local" onclick="showNotebookList()">List Local</button>
    </div>
  </div>
  <div id="notebook">
      <!-- Cells will be added here -->
  </div>

  <!-- Include Marked library for Markdown rendering -->
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script>
    const notebook = document.getElementById('notebook');
    const notebookTitleInput = document.getElementById('notebook-title');
    let cellCounter = 0; // Simple unique ID generator

    // --- Core Cell Functions ---

    function createCellElement(cellId, type) {
        const cell = document.createElement('div');
        cell.classList.add('cell', `${type}-cell`);
        cell.id = cellId;
        return cell;
    }

    function createCodeCell() {
      const cellId = `cell-${++cellCounter}`;
      const cell = createCellElement(cellId, 'code');
      
      const contentDiv = document.createElement('div');
      contentDiv.className = 'cell-content';

      const editor = document.createElement('textarea');
      editor.className = 'code-editor';
      editor.id = `editor-${cellId}`;
      editor.spellcheck = false;
      editor.addEventListener('input', () => adjustTextareaHeight(editor)); // Auto-adjust height

      const outputDiv = document.createElement('div');
      outputDiv.className = 'output';

      contentDiv.appendChild(editor);
      contentDiv.appendChild(outputDiv);
      cell.appendChild(contentDiv);

      // Buttons
      const buttonsDiv = document.createElement('div');
      buttonsDiv.className = 'cell-buttons';
      buttonsDiv.innerHTML = `
        <button class="cell-button" onclick="runCode('${cellId}')" title="Run Code (Ctrl+Enter)">▶ Run</button>
        <button class="cell-button" onclick="minimizeCell('${cellId}')" title="Toggle Minimize">↕</button>
        <button class="cell-button" onclick="deleteCell('${cellId}')" title="Delete Cell">✖</button>
      `;
      cell.appendChild(buttonsDiv);
      
      notebook.appendChild(cell);
      adjustTextareaHeight(editor); // Initial height adjustment
      
      // Add keybinding for Ctrl+Enter
      editor.addEventListener('keydown', (event) => {
          if (event.ctrlKey && event.key === 'Enter') {
              event.preventDefault();
              runCode(cellId);
          }
      });

      return cell; // Return the created cell element
    }

    function createTextCell() {
      const cellId = `cell-${++cellCounter}`;
      const cell = createCellElement(cellId, 'text');

      const contentDiv = document.createElement('div');
      contentDiv.className = 'cell-content';

      const editor = document.createElement('textarea');
      editor.className = 'markdown-editor';
      editor.id = `editor-${cellId}`;
      editor.placeholder = 'Enter Markdown here...';
      editor.addEventListener('input', () => renderMarkdown(cellId)); // Render on input

      const renderDiv = document.createElement('div');
      renderDiv.className = 'markdown-render';
      renderDiv.style.display = 'none'; // Initially hide rendered view

      contentDiv.appendChild(editor);
      contentDiv.appendChild(renderDiv);
      cell.appendChild(contentDiv);

      // Buttons
      const buttonsDiv = document.createElement('div');
      buttonsDiv.className = 'cell-buttons';
      buttonsDiv.innerHTML = `
        <button class="cell-button" onclick="toggleMarkdownView('${cellId}')" title="Toggle Markdown Preview">👁 Preview</button>
        <button class="cell-button" onclick="minimizeCell('${cellId}')" title="Toggle Minimize">↕</button>
        <button class="cell-button" onclick="deleteCell('${cellId}')" title="Delete Cell">✖</button>
      `;
      cell.appendChild(buttonsDiv);

      notebook.appendChild(cell);
      return cell; // Return the created cell element
    }

    function deleteCell(cellId) {
      const cell = document.getElementById(cellId);
      if (cell) {
        cell.style.animation = 'fadeOut 0.3s ease-out forwards'; // Add fade-out animation
        setTimeout(() => cell.remove(), 300); // Remove after animation
      }
    }

    function minimizeCell(cellId) {
      const cell = document.getElementById(cellId);
      if (cell) {
        cell.classList.toggle('minimized');
      }
    }

    // --- Code Execution ---

    async function runCode(cellId) {
      const cell = document.getElementById(cellId);
      if (!cell) return;
      const editor = cell.querySelector('.code-editor');
      const output = cell.querySelector('.output');
      const code = editor.value;
      output.innerHTML = ''; // Clear previous output

      const cleanup = redirectConsole(output);
      try {
        // Use an async function constructor to handle top-level await
        const AsyncFunction = Object.getPrototypeOf(async function(){}).constructor;
        await new AsyncFunction(code)();
      } catch (err) {
        console.error(err); // Log the full error object
      } finally {
        cleanup(); // Restore original console methods
      }
    }

    function redirectConsole(output) {
      const originalLog = console.log;
      const originalError = console.error;
      const originalWarn = console.warn;
      const originalInfo = console.info;
      const originalClear = console.clear; // Capture clear too

      const appendOutput = (content, type) => {
        const msg = document.createElement('div');
        msg.className = type === 'error' ? 'errorDiv' : 'logDiv'; // Simple type distinction
        // Basic object/array formatting attempt
        if (typeof content === 'object' && content !== null) {
            try {
                msg.textContent = JSON.stringify(content, null, 2);
            } catch (e) {
                msg.textContent = content.toString(); // Fallback
            }
        } else {
             msg.textContent = String(content);
        }
        output.appendChild(msg);
      };

      console.log = (...args) => {
        originalLog.apply(console, args); // Keep original behavior
        args.forEach(arg => appendOutput(arg, 'log'));
      };

      console.error = (...args) => {
        originalError.apply(console, args);
        args.forEach(arg => appendOutput(arg, 'error'));
      };
      
      console.warn = (...args) => {
        originalWarn.apply(console, args);
        args.forEach(arg => appendOutput(`WARN: ${arg}`, 'log')); // Treat warn as log for now
      };

      console.info = (...args) => {
        originalInfo.apply(console, args);
        args.forEach(arg => appendOutput(`INFO: ${arg}`, 'log'));
      };
      
      console.clear = () => {
          originalClear.apply(console);
          output.innerHTML = ''; // Clear the output div
      };

      return () => { // Return cleanup function
        console.log = originalLog;
        console.error = originalError;
        console.warn = originalWarn;
        console.info = originalInfo;
        console.clear = originalClear;
      };
    }

    // --- Markdown Handling ---

    function renderMarkdown(cellId) {
        const cell = document.getElementById(cellId);
        if (!cell) return;
        const editor = cell.querySelector('.markdown-editor');
        const renderDiv = cell.querySelector('.markdown-render');
        if (editor && renderDiv && typeof marked === 'function') {
            renderDiv.innerHTML = marked.parse(editor.value);
        } else if (typeof marked !== 'function') {
            console.error("Marked library not loaded.");
        }
    }

    function toggleMarkdownView(cellId) {
        const cell = document.getElementById(cellId);
        if (!cell) return;
        const editor = cell.querySelector('.markdown-editor');
        const renderDiv = cell.querySelector('.markdown-render');
        if (editor.style.display === 'none') {
            // Show editor, hide render
            editor.style.display = 'block';
            renderDiv.style.display = 'none';
        } else {
            // Show render, hide editor
            renderMarkdown(cellId); // Ensure it's up-to-date
            editor.style.display = 'none';
            renderDiv.style.display = 'block';
        }
    }
    
    // --- Utility Functions ---

    function adjustTextareaHeight(textarea) {
      textarea.style.height = 'auto'; // Temporarily shrink
      textarea.style.height = `${textarea.scrollHeight}px`; // Set to scroll height
    }

    function showAlert({ type = 'success', Text = 'Alert' } = {}) {
        var alertBox = document.createElement('div');
        alertBox.className = `Alerts ${type}`; // Add type class
        alertBox.textContent = Text;
        document.body.appendChild(alertBox);
        // Animation handles fade out, but remove element eventually
        setTimeout(() => {
            alertBox.remove();
        }, 3000); // Should match animation total duration
    }

    // --- Persistence (LocalStorage & JSON) ---

    function getNotebookData() {
        const title = notebookTitleInput.value.trim();
        const cells = [];
        notebook.querySelectorAll('.cell').forEach(cell => {
            const id = cell.id;
            if (cell.classList.contains('code-cell')) {
                const editor = cell.querySelector('.code-editor');
                cells.push({ id: id, type: 'code', content: editor?.value || '' });
            } else if (cell.classList.contains('text-cell')) {
                const editor = cell.querySelector('.markdown-editor');
                cells.push({ id: id, type: 'text', content: editor?.value || '' });
            }
        });
        return { title: title || 'Untitled Notebook', cells };
    }

    function loadNotebookData(data) {
        notebookTitleInput.value = data.title || 'Untitled Notebook';
        notebook.innerHTML = ''; // Clear existing cells
        cellCounter = 0; // Reset counter (optional, depends on desired ID behavior)
        if (data.cells && Array.isArray(data.cells)) {
            data.cells.forEach(cellData => {
                let newCell;
                if (cellData.type === 'code') {
                    newCell = createCodeCell(); // Creates cell with a new ID
                    const editor = newCell.querySelector('.code-editor');
                    if (editor) {
                        editor.value = cellData.content || '';
                        adjustTextareaHeight(editor); // Adjust height after setting value
                    }
                } else if (cellData.type === 'text') {
                    newCell = createTextCell(); // Creates cell with a new ID
                    const editor = newCell.querySelector('.markdown-editor');
                    if (editor) {
                        editor.value = cellData.content || '';
                        renderMarkdown(newCell.id); // Render initial markdown
                    }
                }
                 // If you wanted to preserve original IDs (more complex):
                 // You'd need to modify createCell functions to accept an ID
                 // and ensure cellCounter doesn't clash.
            });
        }
    }

    // Local Storage Functions
    function setLocal() {
      const data = getNotebookData();
      if (!data.title || data.title === 'Untitled Notebook') {
        showAlert({ type: 'error', Text: 'Please give a Title to the Notebook before saving locally.' });
        return;
      }
      try {
        localStorage.setItem(`JSNB-${data.title}`, JSON.stringify(data));
        showAlert({ type: 'success', Text: `✔ Notebook "${data.title}" Saved Locally` });
      } catch (error) {
        console.error("Error saving to localStorage:", error);
        showAlert({ type: 'error', Text: `✖ Saving Failed: ${error.message}` });
      }
    }

    function getLocal() {
      const notebookName = notebookTitleInput.value.trim();
      if (!notebookName) {
        showAlert({ type: 'error', Text: 'Please enter a notebook title to load.' });
        return;
      }
      const localNote = localStorage.getItem(`JSNB-${notebookName}`);
      if (localNote) {
        try {
          const content = JSON.parse(localNote);
          loadNotebookData(content);
          showAlert({ type: 'success', Text: `✔ Notebook "${notebookName}" Loaded` });
        } catch (error) {
          console.error("Error parsing localStorage data:", error);
          showAlert({ type: 'error', Text: `✖ Failed to load notebook: ${error.message}` });
        }
      } else {
        showAlert({ type: 'error', Text: `Notebook "${notebookName}" not found in Local Storage.` });
      }
    }

    function showNotebookList() {
      // Create a temporary code cell to display the list
      const tempCell = createCodeCell();
      const editor = tempCell.querySelector('.code-editor');
      const output = tempCell.querySelector('.output');
      
      // Code to list keys and make them clickable
      const listCode = `
console.clear(); // Clear previous logs in this cell
console.log("--- Saved Notebooks (Click to Load Title) ---");
let count = 0;
for (let i = 0; i < localStorage.length; i++) {
   let key = localStorage.key(i);
   if (key.startsWith('JSNB-')) {
      const title = key.substring(5); // Get title part
      const logEntry = document.createElement('div');
      logEntry.className = 'logDiv interactive-log'; // Add class for styling/interaction
      logEntry.textContent = \`• \${title}\`;
      logEntry.style.cursor = 'pointer';
      logEntry.title = 'Click to load this title into the input field';
      logEntry.onclick = () => {
          document.getElementById('notebook-title').value = title;
          console.log(\`Title "\${title}" loaded into input field.\`); 
      };
      // *** Important: Need to append this element directly to the output div ***
      // We bypass the redirected console.log for interactive elements
      const outputDiv = document.getElementById('${tempCell.id}').querySelector('.output');
      if (outputDiv) outputDiv.appendChild(logEntry);
      
      count++;
   }
}
if (count === 0) {
    console.log("No notebooks found in local storage.");
}
console.log("------------------------------------------");
// Add style for clickable logs if needed
const styleExists = document.getElementById('interactive-log-style');
if (!styleExists) {
    const style = document.createElement('style');
    style.id = 'interactive-log-style';
    style.textContent = '.interactive-log:hover { background-color: rgba(255, 255, 255, 0.1); text-decoration: underline; }';
    document.head.appendChild(style);
}
      `;
      
      editor.value = listCode.trim();
      adjustTextareaHeight(editor);
      
      // Run the code to display the list
      // Need a slight delay to ensure the DOM is ready if run immediately
      setTimeout(() => runCode(tempCell.id), 50); 
      
      showAlert({ type: 'success', Text: 'Listing local notebooks in the new cell.' });
    }


    // JSON File Functions
    function downloadNotebook() {
      const data = getNotebookData();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `${data.title.replace(/[^a-z0-9]/gi, '_') || 'Untitled_Notebook'}.json`; // Sanitize filename
      document.body.appendChild(link); // Required for Firefox
      link.click();
      document.body.removeChild(link);
      showAlert({ type: 'success', Text: '✔ Notebook JSON download started' });
    }

    function uploadNotebook(event) {
      const file = event.target.files[0];
      if (!file) return;
      const reader = new FileReader();
      reader.onload = e => {
        try {
          const content = JSON.parse(e.target.result);
           if (typeof content !== 'object' || !Array.isArray(content.cells)) {
               throw new Error("Invalid notebook file format.");
           }
          loadNotebookData(content);
           showAlert({ type: 'success', Text: `✔ Notebook "${content.title || file.name}" Loaded` });
        } catch (error) {
          console.error("Error parsing uploaded file:", error);
          showAlert({ type: 'error', Text: `✖ Failed to load file: ${error.message}` });
        } finally {
            // Reset file input to allow uploading the same file again
             event.target.value = null; 
        }
      };
       reader.onerror = () => {
           showAlert({ type: 'error', Text: '✖ Error reading file.' });
       };
      reader.readAsText(file);
    }

    // --- Event Listeners ---

    document.getElementById('add-code').addEventListener('click', createCodeCell);
    document.getElementById('add-text').addEventListener('click', createTextCell);
    document.getElementById('download-notebook').addEventListener('click', downloadNotebook);
    document.getElementById('upload-trigger').addEventListener('click', () => document.getElementById('upload-notebook').click());
    document.getElementById('upload-notebook').addEventListener('change', uploadNotebook);
    
    // --- Initial Setup ---
    // Optionally create an initial cell on load
    // createCodeCell(); 

  </script>
</body>
</html>
