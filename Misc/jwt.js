function stringToBase64Url(str) {
    const encoder = new TextEncoder();
    const data = encoder.encode(str);
    const base64 = btoa(String.fromCharCode.apply(null, data));
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

function arrayBufferToBase64Url(buffer) {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    const base64 = btoa(binary);
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

function pemToArrayBuffer(pem) {
    const base64 = pem
        .replace(/-----BEGIN PRIVATE KEY-----/g, '')
        .replace(/-----END PRIVATE KEY-----/g, '')
        .replace(/\s+/g, '');
    try {
        const binaryString = atob(base64);
        const len = binaryString.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes.buffer;
    } catch (e) {
        throw new Error(`Failed to decode Base64 from PEM: ${e.message}`);
    }
}

async function signJWT(env) {
    // 1. Extract Private Key from JSON
    const serviceAccountJson = JSON.parse(env.GOOGLE_SERVICE_ACCOUNT_JSON);
    const privateKeyPem = serviceAccountJson.private_key;

    // 2. Define JWT Header (Standard RS256) and payload.
    const header = {
        alg: 'RS256',
        typ: 'JWT',
        kid: serviceAccountJson.private_key_id
    };

    const issuedAt = Math.floor(Date.now() / 1000); // Issued At Time.
    const payload = {
        iss: serviceAccountJson.client_email,
        sub: serviceAccountJson.client_email,
        scope: "https://www.googleapis.com/auth/datastore",
        aud: "https://firestore.googleapis.com/",
        iat: issuedAt,
        exp: issuedAt + 3600
    };
    // 3. Encode Header and Payload (Base64URL)
    const encodedHeader = stringToBase64Url(JSON.stringify(header));
    const encodedPayload = stringToBase64Url(JSON.stringify(payload));

    // 4. Prepare Signing Input
    const signingInput = `${encodedHeader}.${encodedPayload}`;
    const signingInputBytes = new TextEncoder().encode(signingInput);

    try {
        // 5. Prepare the Private Key Buffer (using the extracted PEM)
        const privateKeyBuffer = pemToArrayBuffer(privateKeyPem);
        // 6. Import the Private Key into Web Crypto
        const algorithm = {
            name: 'RSASSA-PKCS1-v1_5',
            hash: 'SHA-256',
        };
        const cryptoKey = await crypto.subtle.importKey(
            'pkcs8',          // Format of the key data (PKCS#8 is standard for these keys)
            privateKeyBuffer, // The key data as an ArrayBuffer
            algorithm,        // Algorithm identifier object
            false,            // Key is not extractable
            ['sign']          // Key usage
        );

        // 7. Sign the Data
        const signatureBuffer = await crypto.subtle.sign(
            algorithm,
            cryptoKey,
            signingInputBytes
        );

        // 8. Encode the Signature (Base64URL)
        const encodedSignature = arrayBufferToBase64Url(signatureBuffer);

        // 9. Assemble the Final JWT
        const jwt = `${signingInput}.${encodedSignature}`;
        return jwt;

    } catch (error) {
        console.error('Error signing JWT with Web Crypto:', error);
    }
}