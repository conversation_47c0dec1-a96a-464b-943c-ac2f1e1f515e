// import { allOpenPnL, setAccessToken } from "./tradeExports.js";

// Set Access Token For Today If Not Already Set.
// await setAccessToken();

// await allOpenPnL();

// import { getRefreshToken } from "./hmac.js";
// await getRefreshToken();



// Test Time taken for Reading from File System.
// deno run --allow-read vv.js
let t0 = performance.now()
const Creds = JSON.parse( Deno.readTextFileSync( "./creds.json" ) )
// console.log(Creds.Stock)
let t1 = performance.now()
// console.log('Time Taken: ', t1 - t0 , 'ms')

GetQuotes()
async function GetQuotes() {
    const response = await (await fetch('https://cdn-ind.testnet.deltaex.org/v2/products?contract_types=perpetual_futures')).json();
    const arr = [ 'BTCUSD', 'ETHUSD', 'SOLUSD', 'XRPUSD', 'ADAUSD' ]
    const data = await (await fetch('https://cdn-ind.testnet.deltaex.org/v2/tickers/' + arr.join(','))).json();
    data.result.forEach((val) => console.log(val.symbol, val.close));
    response.result.forEach((val) => console.log(val.symbol));
}