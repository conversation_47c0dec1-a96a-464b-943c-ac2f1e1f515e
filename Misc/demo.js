import { fyersModel as FyersAPI } from "fyers-api-v3";
import { createHash } from "node:crypto"; // Deno supports "node:****" modules.

// deno run --allow-env --env-file="C:/Users/<USER>/Desktop/Demo_Trade/.env" --allow-read --allow-write --allow-net "C:/Users/<USER>/Desktop/Demo_Trade/Demo_Trade.js"

const creds = JSON.parse(Deno.readTextFileSync("C:/Users/<USER>/Desktop/Demo_Trade/Demo_Creds.json", "utf8"));

async function marketStatus() {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const day = now.getDay();
  // NSE Trading holidays(14 nos.) in 2025 as per Zerodha website.
  const holidays2025 = ["26/02/2025", "14/03/2025", "31/03/2025", "10/04/2025", "14/04/2025", "18/04/2025", "01/05/2025", "15/08/2025", "27/08/2025", "02/10/2025", "21/10/2025", "22/10/2025", "05/11/2025", "25/12/2025"];
  // Check if it's a weekday and market hours (9:15 AM to 3:30 PM) and not a trading holiday.
  return day >= 1 && day <= 5 && !holidays2025.includes(now.toLocaleDateString("en-GB")) && 
    ((hours === 9 && minutes >= 15) ||
      (hours > 9 && hours < 15) ||
        (hours === 15 && minutes <= 30));
}

// One 'Quotes' call to Fyers API supports a maximum of 50 symbols.
const niftySymbols = [
  "NSE:RELIANCE-EQ", // Reliance Industries
  "NSE:TCS-EQ", // Tata Consultancy Services
  "NSE:HDFCBANK-EQ", // HDFC Bank
  "NSE:ICICIBANK-EQ", // ICICI Bank
  "NSE:INFY-EQ", // Infosys
  "NSE:HINDUNILVR-EQ", // Hindustan Unilever
  "NSE:ITC-EQ", // ITC
  "NSE:SBIN-EQ", // State Bank of India
  "NSE:BHARTIARTL-EQ", // Bharti Airtel
  "NSE:HDFC-EQ", // HDFC
  "NSE:KOTAKBANK-EQ", // Kotak Mahindra Bank
  "NSE:LT-EQ", // Larsen & Toubro
  "NSE:BAJFINANCE-EQ", // Bajaj Finance
  "NSE:HCLTECH-EQ", // HCL Technologies
  "NSE:ASIANPAINT-EQ", // Asian Paints
  "NSE:AXISBANK-EQ", // Axis Bank
  "NSE:MARUTI-EQ", // Maruti Suzuki
  "NSE:SUNPHARMA-EQ", // Sun Pharmaceutical
  "NSE:TITAN-EQ", // Titan Company
  "NSE:WIPRO-EQ", // Wipro
  "NSE:ADANIENT-EQ", // Adani Enterprises
  "NSE:ULTRACEMCO-EQ", // UltraTech Cement
  "NSE:BAJAJFINSV-EQ", // Bajaj Finserv
  "NSE:NESTLEIND-EQ", // Nestle India
  "NSE:NTPC-EQ", // NTPC
  "NSE:POWERGRID-EQ", // Power Grid Corporation
  "NSE:ADANIPORTS-EQ", // Adani Ports & SEZ
  "NSE:HDFCLIFE-EQ", // HDFC Life Insurance
  "NSE:TATAMOTORS-EQ", // Tata Motors
  "NSE:DMART-EQ", // Avenue Supermarts
  "NSE:JSWSTEEL-EQ", // JSW Steel
  "NSE:TATASTEEL-EQ", // Tata Steel
  "NSE:M&M-EQ", // Mahindra & Mahindra
  "NSE:HINDALCO-EQ", // Hindalco Industries
  "NSE:SBILIFE-EQ", // SBI Life Insurance
  "NSE:GRASIM-EQ", // Grasim Industries
  "NSE:ADANIGREEN-EQ", // Adani Green Energy
  "NSE:BAJAJ-AUTO-EQ", // Bajaj Auto
  "NSE:TECHM-EQ", // Tech Mahindra
  "NSE:APOLLOHOSP-EQ", // Apollo Hospitals
  "NSE:BRITANNIA-EQ", // Britannia Industries
  "NSE:CIPLA-EQ", // Cipla
  "NSE:DLF-EQ", // DLF
  "NSE:EICHERMOT-EQ", // Eicher Motors
  "NSE:COALINDIA-EQ", // Coal India
  "NSE:DIVISLAB-EQ", // Divi's Laboratories
  "NSE:DRREDDY-EQ", // Dr. Reddy's Laboratories
  "NSE:INDUSINDBK-EQ", // IndusInd Bank
  "NSE:UPL-EQ", // UPL
  "NSE:HEROMOTOCO-EQ", // Hero MotoCorp
];

async function setAccessToken() {
  const response = await fetch(
    "https://1nrp.vercel.app/fyersToken?Token=refresh_token",
    {
      headers: {
        "Cookie": `session_token=${Deno.env.get("BASIC_AUTH_COOKIE")}`,
      },
    },
  );
  const data = await response.json();
  const refreshToken = data.result;
  const appID = Deno.env.get("APP_ID");
  const appSecret = Deno.env.get("APP_SECRET");
  const appHash = createHash("sha256").update(appID + ":" + appSecret).digest(
    "hex",
  );
  const resp = await fetch(
    "https://api-t1.fyers.in/api/v3/validate-refresh-token",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        grant_type: "refresh_token",
        appIdHash: appHash,
        refresh_token: refreshToken,
        pin: Deno.env.get("LOGIN_PIN"),
      }),
    },
  );
  const tokenData = await resp.json();
  const accessToken = tokenData.access_token;
  // Update creds.json file with the new access token.
  creds.access_token = accessToken;
  creds.access_token_date = new Date().toLocaleDateString("en-GB");
  Deno.writeTextFileSync(
    "C:/Users/<USER>/Desktop/Demo_Trade/Demo_Creds.json",
    JSON.stringify(creds, null, 2),
  );
}

// Get quotes from Fyers API.
async function getQuotes(symbols) {
  const fyers = new FyersAPI();
  fyers.setAppId(Deno.env.get("APP_ID"));
  fyers.setRedirectUrl(Deno.env.get("REDIRECT_URL"));
  fyers.setAccessToken(creds.access_token);
  const data = await fyers.getQuotes(symbols);
  return data.d;
}

// Send message to Telegram.
async function sendToTelegram(text) {
  const botToken = Deno.env.get("TG_BOT_TOKEN");
  const chatId = Deno.env.get("TG_CHAT_ID");
  await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chat_id: chatId,
      parse_mode: "HTML",
      text: text,
    }),
  });
}

// Function to fetch quotations.
async function getQuotations() {
  const quotes = await getQuotes(niftySymbols);
  let stocksArray = [];
  for (const quote of quotes) {
    const price = quote.v.lp,
      pct_change = quote.v.chp >= 0 ? quote.v.chp : quote.v.chp * (-1); // To get only the magnitude of the percentage change, as negative % changes are assigned a ' - ' sign.
    const high_price = quote.v.high_price, low_price = quote.v.low_price;
    const new_range_pct = ((high_price - low_price) / price) * 100;
    if (pct_change > 0 && quote.v.lp < 20000 && new_range_pct < 4) {
      stocksArray.push({
        symbol: quote.n,
        change: pct_change,
        range_pct: new_range_pct,
        details: {
          high: quote.v.high_price,
          low: quote.v.low_price,
        },
      });
    }
  }
  // Sort stocks by percentage change and get top 5
  stocksArray.sort((a, b) => b.change - a.change);
  creds.Stock = stocksArray.slice(0, 5).map((stock) => stock.symbol);
  creds.Details = stocksArray.slice(0, 5).map((stock) => stock.details);
  Deno.writeTextFileSync(
    "C:/Users/<USER>/Desktop/Demo_Trade/Demo_Creds.json",
    JSON.stringify(creds, null, 2),
  );
}

// Strategy for Demo Trading.
async function strategy() {
  const symbols = creds.Stock; // This is currently an array of symbols.
  let entered = false;
  async function strategyEntry() {
    if (entered) return; // Skip if already entered a trade.
    try {
      // Get quotes for all symbols at once.
      const quotes = await getQuotes(symbols);
      // Loop through each quote and check for breakouts
      for (let i = 0; i < quotes.length; i++) {
        const quote = quotes[i];
        const currentPrice = quote.v.lp;
        const { high, low } = creds.Details[i]; // Get corresponding details for this symbol.
        const quantity = Math.floor(
          (creds.available_funds * 0.99) / currentPrice,
        ); // 99% of available funds.

        if (currentPrice > high) { // Long Entry.
          entered = true;
          clearInterval(entryInterval);
          creds.Stock = symbols[i]; // Save the selected stock.
          creds.order_type = "LONG"
          creds.trade_count += 1; // Increase total trade count by 1.
          creds.entry_price = currentPrice;
          creds.quantity = quantity;
          creds.stopLevel = Number((currentPrice * 0.996).toFixed(1)); // 0.4% below entry
          Deno.writeTextFileSync(
            "C:/Users/<USER>/Desktop/Demo_Trade/Demo_Creds.json",
            JSON.stringify(creds, null, 2),
          );

          const message = `
              🟦 DEMO TRADE ENTRY - <b>LONG</b>
              <b>Entered :</b> <i>${creds.Stock}</i>
              <b>Price:</b> ${currentPrice}
              <b>Quantity:</b> ${quantity}
              <b>Initial Stop Loss:</b> ${creds.stopLevel}
              `;
          await sendToTelegram(message);
          break; // Exit loop after entering trade.
        } else if (currentPrice < low) { // Short Entry
          entered = true;
          clearInterval(entryInterval);
          creds.Stock = symbols[i]; // Save the selected stock
          creds.order_type = "SHORT";
          creds.trade_count += 1; // Increase total trade count by 1.
          creds.entry_price = currentPrice;
          creds.quantity = quantity;
          creds.stopLevel = Number((currentPrice * 1.004).toFixed(1)); // 0.4% above entry
          Deno.writeTextFileSync(
            "C:/Users/<USER>/Desktop/Demo_Trade/Demo_Creds.json",
            JSON.stringify(creds, null, 2),
          );

          const message = `
              🟥 DEMO TRADE ENTRY - <b>SHORT</b>
              <b>Entered :</b> <i>${creds.Stock}</i>
              <b>Price:</b> ${currentPrice}
              <b>Quantity:</b> ${quantity}
              <b>Initial Stop Loss:</b> ${creds.stopLevel}
              `;
          await sendToTelegram(message);
          break; // Exit loop after entering trade.
        }
      }
    } catch (error) {
      console.error("Error in strategyEntry:", error);
      await sendToTelegram(`⚠️ Error in strategy entry: ${error.message}`);
    }
  }

  // Enter the position
  const entryInterval = setInterval(strategyEntry, 60000); // 60 seconds

  async function strategyCheck() {
    if (entered) {
      // Use proper time zone handling
      const now = new Date();
      const ist = { timeZone: "Asia/Kolkata" };
      const istTime = now.toLocaleString("en-US", ist);
      const istDate = new Date(istTime);

      const hours = istDate.getHours();
      const minutes = istDate.getMinutes();

      // Market closing time (15:30 IST)
      const isMarketClosing = hours === 15 && minutes >= 25;

      const quotes = await getQuotes([`${creds.Stock}`]);
      const nowPrice = quotes[0].v.lp;

      if (
        creds.order_type == "SHORT" && nowPrice > creds.stopLevel ||
        creds.order_type == "LONG" && nowPrice < creds.stopLevel ||
        isMarketClosing
      ) {
        clearInterval(checkInterval);

        const pnl = (creds.order_type === "LONG")
          ? (nowPrice - creds.entry_price) * creds.quantity
          : (creds.entry_price - nowPrice) * creds.quantity;

        // Update all risk level funds.
        creds.available_funds += Number(pnl.toFixed(2));
        creds.onePctRisk += +((pnl * (2.5)).toFixed(2)); // 0.4 * 2.5 = 1
        creds.twoPctRisk += Number((pnl*5).toFixed(2));// 0.4 * 5 = 2

        Deno.writeTextFileSync(
          "C:/Users/<USER>/Desktop/Demo_Trade/Demo_Creds.json",
          JSON.stringify(creds, null, 2),
        );
        const pnlPercentage =
          ((pnl / (creds.entry_price * creds.quantity)) * 100).toFixed(2);
        // More Emojis => 🔴 🟢 🔵 🟠 🔘 🟡 🟩 🟨 🟧 🟪 🟥 🟦 ⚪
        const pnlTag = pnl >= 0 ? "🟢 PROFIT" : "🔴 LOSS";

        const message = `
            🟨 DEMO TRADE EXIT
            <b>${pnlTag}</b>
            <b>Stock:</b> <i>${creds.Stock}</i>
            <b>Exit Price:</b> ${nowPrice}
            <b>P&L Amount:</b> ₹ ${pnl.toFixed(2)}
            <b>P&L %:</b> ${pnlPercentage}%
            <b>Trade Type:</b> ${creds.order_type}
            `;
        await sendToTelegram(message);
        // Delete the log file.
        const logPath = `C:/Users/<USER>/Desktop/Demo_Trade/${new Date().toISOString().split('T')[0]}.log`;
        // Deno.removeSync(logPath);
        entered = false; // Reset entered flag.
      } else {
        // Update trailing stop loss.
        if (creds.order_type == "LONG") {
            const newStopLevel = nowPrice * 0.996;
            creds.stopLevel = newStopLevel > creds.stopLevel
                ? +(newStopLevel.toFixed(1))  // newStopLevel.toFixed(1) converts the number to a string with one decimal place. + converts it back to a number. +() is similar to Number().
                : +creds.stopLevel;
        } else {
            const newStopLevel = nowPrice * 1.004;
            creds.stopLevel = newStopLevel < creds.stopLevel
                ? +(newStopLevel.toFixed(1))
                : +creds.stopLevel;
        }
        Deno.writeTextFileSync(
            "C:/Users/<USER>/Desktop/Demo_Trade/Demo_Creds.json",
            JSON.stringify(creds, null, 2)
          );
        }
      }
    }
  const checkInterval = setInterval(strategyCheck, 60000); // 60 seconds
}


// The main Trading Script.
async function mainScript() {
  try {

    // Check if market is open
    const isMarketOpen = await marketStatus();
    if (!isMarketOpen) {
      await sendToTelegram(
        "🟧<b> Market is closed. Demo Trading will not start.</b>",
      );
      return;
    }
    // Set access token.
    await setAccessToken();
    
    await sendToTelegram("🟪<b> Starting Demo Trading System... </b>");

    // Get quotations and save selected stock
    await getQuotations();

    // Run strategy
    await strategy();
  } catch (error) {
    await sendToTelegram(`🟧 Error: ${error}`);
    console.error(error);
  }
}

mainScript();
