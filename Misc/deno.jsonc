{ // Run "deno task top" in the terminal to get top 50 liquid stocks.
  "tasks":
    {

        "run": "deno run --allow-env --env-file='./.env' --allow-read --allow-write --allow-net './Demo_Test.js'",
        "api": "deno run --allow-env --env-file='C:/Users/<USER>/Desktop/Demo_Test/.env' --allow-read --allow-write --allow-net 'C:/Users/<USER>/Desktop/Demo_Test/api.js'",
        "vv": "deno run --allow-env --env-file='./.env' --allow-read --allow-write --allow-net './vv.js'",
        "cdl": "wrangler deploy",
        "cll": "wrangler workflows trigger Trading-WorkFlow"
        // "fmt": "js-beautify trade.js"
    },
  "imports":
    {
        "dom-parser": "npm:dom-parser@^1.1.5",
        "wrangler": "npm:wrangler"
    }
}