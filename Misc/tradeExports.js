const creds = JSON.parse(Deno.readTextFileSync("./Test_Creds.json", "utf8"));

// One 'Quotes' call to Fyers API supports a maximum of 50 symbols.
export const niftySymbols = [ "NSE:HDFCBANK-EQ", "NSE:KOTAKBANK-EQ", "NSE:RELIANCE-EQ", "NSE:ABB-EQ", "NSE:BAJFINANCE-EQ", "NSE:ADANIENT-EQ", "NSE:APOLLOHOSP-EQ", "NSE:ASHOKLEY-EQ", "NSE:ASIANPAINT-EQ", "NSE:AXISBANK-EQ", "NSE:BEL-EQ", "NSE:BHARTIARTL-EQ", "NSE:BRITANNIA-EQ", "NSE:CIPLA-EQ", "NSE:DABUR-EQ", "NSE:DRREDDY-EQ", "NSE:EICHERMOT-EQ", "NSE:FEDERALBNK-EQ", "NSE:HAL-EQ", "NSE:HCLTECH-EQ", "NSE:HDFCLIFE-EQ", "NSE:HEROMOTOCO-EQ", "NSE:HINDALCO-EQ", "NSE:HINDUNILVR-EQ", "NSE:ICICIBANK-EQ", "NSE:INDIGO-EQ", "NSE:IOC-EQ", "NSE:ITC-EQ", "NSE:LT-EQ", "NSE:M&M-EQ", "NSE:MARUTI-EQ", "NSE:ONGC-EQ", "NSE:SBIN-EQ", "NSE:SUNPHARMA-EQ", "NSE:TATAMOTORS-EQ", "NSE:TATASTEEL-EQ", "NSE:TCS-EQ", "NSE:TITAN-EQ", "NSE:TRENT-EQ", "NSE:VEDL-EQ", "NSE:WIPRO-EQ", "NSE:ACC-EQ", "NSE:ADANIPORTS-EQ", "NSE:INDHOTEL-EQ", "NSE:AMBUJACEM-EQ", "NSE:ANGELONE-EQ", "NSE:APOLLOTYRE-EQ", "NSE:AUROPHARMA-EQ", "NSE:BAJAJ-AUTO-EQ", "NSE:BAJAJFINSV-EQ", "NSE:MAZDOCK-EQ", "NSE:BANKBARODA-EQ", "NSE:BHARATFORG-EQ", "NSE:BHEL-EQ", "NSE:BPCL-EQ", "NSE:CAMS-EQ", "NSE:CANBK-EQ", "NSE:CHOLAFIN-EQ", "NSE:GRASIM-EQ", "NSE:COALINDIA-EQ", "NSE:COFORGE-EQ", "NSE:COLPAL-EQ", "NSE:CUMMINSIND-EQ", "NSE:DIVISLAB-EQ", "NSE:DLF-EQ", "NSE:EXIDEIND-EQ", "NSE:GAIL-EQ", "NSE:GODREJCP-EQ", "NSE:HAVELLS-EQ", "NSE:HDFCAMC-EQ", "NSE:HINDPETRO-EQ", "NSE:IRFC-EQ", "NSE:JINDALSTEL-EQ", "NSE:JIOFIN-EQ", "NSE:JSWSTEEL-EQ", "NSE:KALYANKJIL-EQ", "NSE:LICHSGFIN-EQ", "NSE:LICI-EQ", "NSE:LUPIN-EQ" ];

// Check if market is open.
export async function marketStatus() {
  const time = new Date().toLocaleString("en-GB", {timeZone: "Asia/Kolkata"});
  const now = new Date(time);
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const day = now.getDay();
  // NSE Trading holidays(14 nos.) in 2025 as per Zerodha website.
  const holidays2025 = ["26/02/2025", "14/03/2025", "31/03/2025", "10/04/2025", "14/04/2025", "18/04/2025", "01/05/2025", "15/08/2025", "27/08/2025", "02/10/2025", "21/10/2025", "22/10/2025", "05/11/2025", "25/12/2025"];
  // Check if it's a weekday and market hours (9:15 AM to 3:30 PM) and not a trading holiday.
  return day >= 1 && day <= 5 && !holidays2025.includes(new Date().toLocaleDateString("en-GB")) &&
    ((hours === 9 && minutes >= 15) ||
      (hours > 9 && hours < 15) ||
        (hours === 15 && minutes <= 30));
}

// Create HMAC using Web APIs.
export async function createHmac(text) {
  const secret = Deno.env.get("SHARED_SECRET");
  // Import the key.
  const keyBytes = new TextEncoder().encode(secret);
  const cryptoKey = await crypto.subtle.importKey(
    "raw", // Raw format for the key data.
    keyBytes,
    { name: "HMAC", hash: "SHA-256" },
    false, // Not extractable.
    ["sign", "verify"] // For both signing and verifying.
  );
  // Then sign the data.
  const dataBytes = new TextEncoder().encode(text);
  const hmacBuffer = await crypto.subtle.sign( { name: "HMAC" }, cryptoKey, dataBytes );
  // Convert to hex string.
  const hmacArray = Array.from(new Uint8Array(hmacBuffer));
  const hmacHex = hmacArray.map(b => b.toString(16).padStart(2, "0")).join('');
  return hmacHex;
}

// Get Today's Access Token.
export async function getTodaysAccessToken() {
  const response = await fetch("https://1nrp.vercel.app/fyersToken?Token=access_token", {
      headers: {
        "Cookie": `session_token=${Deno.env.get("BASIC_AUTH_COOKIE")}`,
      },
    });
  const accessToken = await response.json();
  // Update creds.json file with the new access token.
  creds.access_token = accessToken;
  creds.access_token_date = new Date().toLocaleDateString("en-GB");
  Deno.writeTextFileSync("./Test_Creds.json",JSON.stringify(creds, null, 2));
}

// Get quotes from Fyers API.
export async function getQuotes(symbols) {
    const response = await fetch('https://api-t1.fyers.in/data/quotes?symbols=' + encodeURIComponent(symbols), {
      headers: {
        "Authorization": Deno.env.get("FYERS_APP_ID") + ':' + creds.access_token
      },
    });
    const data = await response.json();
    return data.d;
}

// Function to fetch quotations
export async function getQuotations() {
  const quotes = await getQuotes(niftySymbols);
  let stocksArray = [];
  for (const quote of quotes) {
    const price = quote.v.lp,
      pct_change = quote.v.chp >= 0 ? quote.v.chp : quote.v.chp * (-1); // To get only the magnitude of the percentage change, as negative % changes are assigned a ' - ' sign.
    const high_price = quote.v.high_price, low_price = quote.v.low_price;
    const new_range_pct = ((high_price - low_price) / price) * 100;
    if (pct_change > 0 && quote.v.lp < 20000 && new_range_pct < 4) {
      stocksArray.push({
        symbol: quote.n,
        change: pct_change,
        range_pct: new_range_pct,
        details: {
          high: quote.v.high_price,
          low: quote.v.low_price,
        },
      });
    }
  }
  // Sort stocks by percentage change and get top 5
  stocksArray.sort((a, b) => b.change - a.change);
  creds.Stock = stocksArray.slice(0, 20).map((stock) => stock.symbol);
  creds.Details = stocksArray.slice(0, 20).map((stock) => stock.details);
  Deno.writeTextFileSync("./Test_Creds.json", JSON.stringify(creds, null, 2));
}

// Send message to Telegram
export async function sendToTelegram(text) {
  const botToken = Deno.env.get("TG_BOT_TOKEN");
  const chatId = Deno.env.get("TG_CHAT_ID");
  await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chat_id: chatId,
      parse_mode: "HTML",
      text: text,
    }),
  });
}

// See all open PnL.
export async function allOpenPnL() {
  const stocks = JSON.parse(Deno.readTextFileSync("./ActiveStocks.json", "utf8"));
  const stocksArr = Object.keys(stocks);

  const quotes = await getQuotes(stocksArr);
  let openPnLs = 0;
  for ( const quote of quotes) {
    const price = quote.v.lp;
    const symbol = quote.n;
    const stock = stocks[symbol];
    const openPnL = (stock.orderType === "LONG") ?
      (price - stock.entryPrice) * stock.quantity :
      (stock.entryPrice - price) * stock.quantity;
    openPnLs += openPnL;
    console.log(`Stock: ${symbol} - Open P&L: ₹ ${openPnL.toFixed(2)}`);
  } 
  console.log('All Open PnL:', openPnLs.toFixed(2));
}

// Format trade log for text file.
export function formatTradeLog(logs) {
  let text = "===== DEMO TRADING SYSTEM - TRADE LOG =====\n\n";

  for (const log of logs) {
    text += `TRADE #${log.serialNo}\n`;
    text += `Symbol: ${log.symbol}\n`;
    text += `Type: ${log.orderType}\n`;
    text += `Entry Price: ${log.entryPrice}\n`;
    text += `Exit Price: ${log.exitPrice}\n`;
    text += `Quantity: ${log.quantity}\n`;
    text += `Initial Stop Loss: ${log.initialStopLoss}\n`;
    text += `P&L: ₹${log.pnl} (${log.pnlPercentage}%)\n`;
    text += `Result: ${log.result}\n`;
    text += `Entry Time: ${log.entryTime}\n`;
    text += `Exit Time: ${log.exitTime}\n`;
    text += `==============================\n\n`;
  };

  return text;
}