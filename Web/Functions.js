// Fyers Functions.

// Auth_Code Generation URL: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=SPXXXXE7-100&redirect_uri=https://1nrp.vercel.app/fyersToken&response_type=code&state=NRP-Algo
// THe 'client_id' in the Auth_Code Generation URL is the same as the Fyers 'app_id'.
// Update the Refresh Token every 15 days by visiting the Auth_Code Generation URL.

export async function fyersToken(req, env) {
  const UPSTASH = {
    url: env.UPSTASH_REDIS_REST_URL,
    token: env.UPSTASH_REDIS_REST_TOKEN,

    GET: async (key) => {
      const response = await fetch(`${UPSTASH.url}/GET/${key}`, {
        headers: {
          'Authorization': `Bearer ${UPSTASH.token}`
        }
      });
      const data = await response.text(); // To reduce overhead of parsing and again stringifying to send to client, it is sent as text.
      return data;
    },
    SET: async (key, value) => {
      const response = await fetch(`${UPSTASH.url}/SET/${key}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'text/plain',
          'Authorization': `Bearer ${UPSTASH.token}`
        },
        body: value
      });
      const data = await response.text(); // To reduce overhead of parsing and again stringifying to send to client, it is sent as text.
      return data;
    },
    LPUSH: async (key, value) => {
      const response = await fetch(`${UPSTASH.url}/LPUSH/${key}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'text/plain',
          'Authorization': `Bearer ${UPSTASH.token}`
        },
        body: value
      });
      const data = await response.text(); // To reduce overhead of parsing and again stringifying to send to client, it is sent as text.
      return data;
    }
  }
    try {
      const appHash = await env.FYERS_APP_HASH; // The 'appID' and 'appSecret' are already Hashed and stored in environment variables.
      const url = new URL(req.url);
      const auth_code = url.searchParams.get('auth_code');
      const Token = url.searchParams.get('Token');
      
    if ( auth_code ) {
      // Save the date of creating the refresh_token to "Miscellaneous" key in "Cloud Notes".
      const timestamp = Date.now() + (15 * 24 * 60 * 60 * 1000);
      const expiryDate = new Date(timestamp).toLocaleDateString('en-GB', { timeZone: 'Asia/Kolkata' }); // Date() method Automatically accounts for monthly variations of number of days.
      const kvText = `⭐ ⭐ ⭐ Fyers Refresh Token Was Last Saved to KV database at: [ ${ new Date().toLocaleString('en-GB', {timeZone: 'Asia/Kolkata'}) } ]. It Will Expire On: [ ${expiryDate} At 6:00 AM ]. Re-Generate This Token On Or Before Expiry Date By Visiting The Auth_Code Generation URL: [ https://api-t1.fyers.in/api/v3/generate-authcode?client_id=SPXXXXE7-100&redirect_uri=https://1nrp.vercel.app/fyersToken&response_type=code&state=NRP-Algo ]. The "client_id" Is Same As The Fyers "app_id" Which Can Be Found On The Fyers API Dashboard.`;
      await fetch(`${UPSTASH.url}/LPUSH/Miscellaneous/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'text/plain',
          'Authorization': `Bearer ${UPSTASH.token}`
        },
        body: kvText
      });
      // Generate and save the refresh_token.
      const response = await fetch('https://api-t1.fyers.in/api/v3/validate-authcode', {
        method: 'POST', headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ grant_type: 'authorization_code', appIdHash: appHash, code: auth_code }) 
      });

      const { refresh_token: refreshToken } = await response.json();
      await UPSTASH.SET('FYERS_REFRESH_TOKEN', refreshToken);
      return new Response('Refresh Token saved successfully to KV.');

    } else if ( Token === 'refresh_token' ) {
      const data = await UPSTASH.GET('FYERS_REFRESH_TOKEN');
      return new Response(data);

    } else if ( req.method === 'POST' && Token === 'access_token' ) {
      const data = await UPSTASH.SET('FYERS_ACCESS_TOKEN', req.body);
      return new Response(data);

    } else if ( req.method === 'GET' && Token === 'access_token' ) {
      const data = await UPSTASH.GET('FYERS_ACCESS_TOKEN');
      return new Response(data);

    } else {
      return new Response('Invalid Request. No Token parameter provided in the request URL.');
    }
    } catch (error) {
      console.error(error);  // Log the error for debugging.
      return new Response('An error occurred.', { status: 500 });
    }
}

// Blob Server.
export async function BlobServer(req) {

  if (req.method === 'POST') {  // The token generation request is a "POST" request.
    const { handleUpload } = await import('@vercel/blob/client');
    const body = req.body;
    
    try {
      const jsonResponse = await handleUpload({
        body,
        request: req,
        onBeforeGenerateToken: async (_pathname, /* clientPayload */) => {
          return {
            allowedContentTypes: [ 'image/*', 'video/*', 'audio/*', 'text/*', 'application/*' ], // Accept all common MIME types.
            addRandomSuffix: false,
            tokenPayload: "Blob Upload Completed.",
          };
        },
        onUploadCompleted: async (_blob, tokenPayload) => {
          return tokenPayload;
        },
      });
      return new Response(jsonResponse);
    } catch (error) {
      console.error('Error while handling the upload:', error);
      return new Response( JSON.stringify({ error: error.message }) );
    }

  } else {
    const TASK = new URL(req.url).searchParams.get('TASK');
    const { list, copy, del } = await import('@vercel/blob');

    if (TASK === 'List') {
      try {
        const FOLDER_NAME = new URL(req.url).searchParams.get('FOLDER_NAME');
        const { blobs } = await list({ prefix: FOLDER_NAME });
        return new Response(blobs);
      } catch (error) {
        console.error(error);
        return new Response( JSON.stringify({ error: 'An error occurred.' }) );
      }
    } else if (TASK === 'Delete') {
      try {
        const URL = new URL(req.url).searchParams.get('URL');
        await del(URL);
        return new Response( JSON.stringify({ message: 'Blob deleted.' }) );
      } catch (error) {
        console.error(error);
        return new Response( JSON.stringify({ error: 'An error occurred.' }) );
      }
    } else if (TASK === 'Rename') {  // Vercel Blob SDK does not provide a renaming feature. So this is a workaround.
      try {
        const params = Object.fromEntries( new URL(req.url).searchParams );
        const { FILE_URL, NEW_PATHNAME } = params;
        await copy(FILE_URL, NEW_PATHNAME, { access: 'public' });
        await del(FILE_URL);
        return new Response( JSON.stringify({ message: 'Blob renamed.' }) );
      } catch (error) {
        console.error(error);
        return new Response( JSON.stringify({ error: 'An error occurred.' }), { ststus: 500 } );
      }
    } else {
      return new Response( JSON.stringify({ error: 'Invalid request.' }), { status: 400 } );
    }
  }
};

export async function TeraboxCloudflare(request) {
  const link = new URL(request.url);
  const shortURL = link.searchParams.get('URL');
  const Cache = link.searchParams.get("CacheOption") === "Yes"; // Convert Cache to boolean.
  const Token = link.searchParams.get("AccessToken");

  if (Token !== "cloudF-code-0088") {
    return new Response("Wrong Access Token Code. Request Is Unauthorized", { status: 401 });
  }
  if (!shortURL) {
    return new Response("URL parameter is required", { status: 403 });
  }

  try {
  // Not Adding The Cookie Header Only Fetches 30 Seconds Of The Video. Get it by Inspecting the Network Tab of the Terabox Webpage.
  const tbHeaders = new Headers();
  const Cookie = 'csrfToken=d4XE8GgrDLUWGi0VrMhBGhoj; browserid=MplsviPBWAXvHjkmrC50dtNbTtZ-TZoz-SwtrA2TOr1GDCXPUyQNkKiIEg0=; lang=en; TSID=TVXQQOOR4DxS9SWcPPDzjRWcwWUuHZgC; __bid_n=196b99dccf129ec2e74207; g_state={"i_l":0}; ndus=YuAqZKCteHuiYFHuyxk-Lx0nXsIscEwH1afty9gM; ndut_fmt=686106BA8EABE19FE3B1B80A4B2D9A932FEFE4938C490620A3404BE764E6DB76';
  tbHeaders.set('Cookie', Cookie);

  const apiUrl = `https://www.terabox.app/api/shorturlinfo?shorturl=${shortURL}&root=1`;
  const infoResponse = await fetch(apiUrl, Cache ? { cf: { cacheTtl: 31536000, cacheEverything: true } } : {});
  
  if (!infoResponse.ok) {
    throw new Error(`Failed to fetch short URL info: ${infoResponse.statusText}`);
  }

  const { shareid, uk, list: [ { fs_id } ] } = await infoResponse.json(); // Accessing the list array in  infoData, then extracting the 'fs_id' property from the first object in that array. To access from third element, write " list: [,,{ fs_id } ] ".
  //const m3u8Url = `https://www.terabox.app/share/streaming?uk=${uk}&shareid=${shareid}&type=M3U8_AUTO_360&fid=${fs_id}&sign=1&timestamp=1&clienttype=1&channel=1`;
  const m3u8Url = `https://dm.1024tera.com/share/streaming?uk=${uk}&shareid=${shareid}&type=M3U8_FLV_264_480&fid=${fs_id}&sign=e079e32543e8a8e08d13776fc9e6c3fdaec67489&timestamp=1746887076&jsToken=1F1DA1DCB0EDBEBF4D4026783231FF6C9B4DB9B51B9760AA7BDD646E468831907207153BCE380C6E022CF085CFB889982CF47DC211FCCFBE69AE839F30F263E0CEBA873FED7A8A6AE0102FEF4265151A7491413528A981953B8CCE16C5FD12EE&esl=1&isplayer=1&ehps=1&clienttype=0&app_id=250528&web=1&channel=dubox&short_link=5`;
  
  const m3u8Response = await fetch(m3u8Url, { headers: tbHeaders });
  const head = {
    'Content-Type': 'application/x-mpegURL',
    'Access-Control-Allow-Origin': 'https://1nrp.github.io',
    'Cache-Control': 'max-age=259200'
  };
  const response = new Response(m3u8Response.body, {
    status: 200,
    headers: head
  });

  return response;
  } catch (error) {
    console.error('Error:', error);
    return new Response('An error occurred.', { status: 500 });
  }
}

export async function corsProxy(req) {
  try {
    const URL = new URL(req.url).searchParams.get('URL');
    const response = await fetch(URL);
    
    const head = new Headers(response.headers)
      .set('Cache-Control', 'public, max-age=3600')
      .set('Access-Control-Allow-Origin', 'https://1nrp.github.io');

    return new Response( await response.blob(), { status: 200, headers: head });

  } catch (error) {
    console.error('Error:', error);
  }
};

export async function JSNotebook(req, env) {
  if (req.method == 'GET') {
    try {
      const response = await env.KV.get('JAVASCRIPT_NOTEBOOK');
      return new Response( await response.text(), { status: 200 } );
    } catch (error) {
      return new Response( JSON.stringify({ error: 'An error occurred while fetching Notebook from KV.' }), {status: 500} );
    }
      
    } else if (req.method == 'POST') {
    try {
      const response = await env.KV.put('JAVASCRIPT_NOTEBOOK', req.body);
      return new Response( await response.text() );
    } catch (error) {
      return new Response( JSON.stringify({ error: 'An error occurred while saving Notebook to KV.' }), {status: 500} );
    }
    } else {
      return new Response( 'notFoundError: Neither GET nor POST request was received.' );
    }
}

export { saveNote, deleteNote, getNote }; // Export The Note Functions.
async function saveNote(req, env) {
  try {
    const { lastSentence, REDIS_KEY } = JSON.parse(req.body);
    const response = await fetch(`${env.KV_REST_API_URL}/LPUSH/${REDIS_KEY}/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Authorization': `Bearer ${env.KV_REST_API_TOKEN}`
      },
      body: lastSentence
    });
    return new Response( await response.json() );
  } catch (error) {
    console.error('Error Name: ', error.name, 'Error Message: ', error.message);
    return new Response('An error occurred while sending to KV.', { status: 500 });
  }
};

async function getNote(req, env) {
  try {
    const REDIS_KEY = new URL(req.url).searchParams.get('REDIS_KEY');
    const response = await fetch(`${env.KV_REST_API_URL}/LRANGE/${REDIS_KEY}/0/-1/`, {
      headers: {
        'Authorization': `Bearer ${env.KV_REST_API_READ_ONLY_TOKEN}`,
      }
    });
    return new Response( await response.json() );
  } catch (error) {
    console.error('Error:', error);
    return new Response('An error occurred while sending to KV.', { status: 500 });
  }
};

async function deleteNote(req, env) {
  try {
    const REDIS_KEY = new URL(req.url).searchParams.get('REDIS_KEY');
    const response = await fetch(`${env.KV_REST_API_URL}/LREM/${REDIS_KEY}/0/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Authorization': `Bearer ${env.KV_REST_API_TOKEN}`
      },
      body: req.body,
    });
    return new Response( await response.json() );
  } catch (error) {
    console.error('Error: ', error);
    return new Response('An error occurred while sending to KV.', { status: 500 });
  }
}