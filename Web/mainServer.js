import express, { json, text } from 'express';
import basicAuth from 'express-basic-auth';
import cookieParser from 'cookie-parser';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import cors from 'cors';
const app = express();
// Workaround to support loading files in ES module as it does not support '__dirname' by default.
const __dirname = dirname(fileURLToPath(import.meta.url));

/*
// For supporting require() for CommonJS modules in an ES module. For that add:
import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const someModule = require('some-module');
*/

// CORS options.
const corsOptions = {
  origin: ['https://1nrp.github.io' /*, 'https://1nrp.gov.in' */ ],  // Allow only these origins.
  //credentials: true,  // Allow credentials (cookies, HTTP auth, etc.) through CORS.
  optionsSuccessStatus: 200
};

// Middleware to enable CORS and parse cookies & JSON.
app.use(cors(corsOptions));
app.use(cookieParser());
app.use(json()); // Middleware to automatically parse json bodies in POST requests.
app.use(text()); // Middleware to handle plain text in POST request.

// Hardcoded credentials and session ID
const username = '2266'; // Fixed username
const password = '4343'; // Fixed password
const sessionId = 'eyjp748iurif23432jmbhkj9l8ujvgtd8olgsw480pjkhnbgvd387884975ekpjgtref3h4n23n4m9m97nbvdh0lkgff93xj9043gdh';  // Hardcoded session ID. Consider using dynamic session tokens for production purpose.
// Basic Authentication Middleware
const basicAuthMiddleware = basicAuth({
  users: { [username]: password },
  challenge: true,  // This ensures that a Basic Auth prompt is shown.
  realm: 'Be a Learning Machine.'
});

// Custom Authentication Middleware
const authenticateUser = (req, res, next) => {
  const token = req.cookies.session_token;
  if (token === sessionId) {
    // User is already authenticated.
    // console.log('Session Token:', token);
    return next();
  }

  // Apply Basic Auth if no valid session token is present
  basicAuthMiddleware(req, res, () => {
    if (res.statusCode === 401) {
      // Basic Auth failed
      return;
    }
    // Set session cookie upon successful Basic Auth
    res.cookie('session_token', sessionId, {
      maxAge: 2 * 60 * 60 * 1000,  // Cookie expires after 2 hours.
      secure: true,                // Set to true if using HTTPS
      httpOnly: true,              // Cookie cannot be accessed via JavaScript
      sameSite: 'Strict'           // Cookies are only sent for same-site requests
    });
    next();
  });
};

// Serve the HTML (Home) page. Applying Basic Auth to this '/' route may block requests to 'NodeJS/Play' routes too.
app.get('/', (req, res, next) => {
  // Try to send index.html if it exists, otherwise let express.static handle it (or return 404)
  res.sendFile(join(__dirname, 'index.html'), (err) => {
      if (err) next(); // Pass to next middleware if index.html is not found, so express.static can try
  });
});

app.get('/upload.js', (_req, res) => {
  res.sendFile(join(__dirname, 'vercelFileUploadFunctionBundled.js')); 
});

// Apply authentication to desired routes
const protectedRoutes = ['/fyerstoken', '/blob', '/getnote', '/savenote', '/deletenote', '/blobserver'];

app.use((req, res, next) => {
  if (protectedRoutes.includes(req.path.toLowerCase())) {  // Converted to Lower Case to avoid Authentication bypassing(by altering the case of the URL path) as Express.js routes are Case Insensitive by default.
    console.log(`Requested Protected Route: ${req.path}`);
    authenticateUser(req, res, next);
  } else {
    next();
  }
});

// Import route handlers
import { saveNote, deleteNote, getNote } from './NodeJS/Note/NoteFunctions.js';
import { deleteLink, getM3U8, getLink, saveLink, tgChannels, checkIfExists } from './NodeJS/Play/PlayFunctions.js';
import cronJob from './NodeJS/Database-Deletion-Prevention-CRON-Job.js';
import { fyersToken } from './NodeJS/fyersToken.js';
import javaScriptNotebook from './NodeJS/JSNB.js';
import BlobServer from './NodeJS/BlobServer.js';
import CORS_Proxy from './NodeJS/CORS-Proxy.js';

// Apply Route Handlers.
app.get('/Blob', (req, res) => { res.sendFile(join(__dirname, 'Blob.html')); });
app.post('/deleteNote', deleteNote);
app.get('/getNote', getNote);
app.post('/saveNote', saveNote);
app.post('/deleteLink', deleteLink);
app.get('/getM3U8', getM3U8);
app.get('/getLink', getLink);
app.post('/saveLink', saveLink);
app.get('/KV-And-Blob-Database-Deletion-Prevention-CRON-Job', cronJob);
app.post('/checkLinkExistence', checkIfExists);
app.get('/tgChannels', tgChannels);
app.route('/BlobServer').get(BlobServer).post(BlobServer);
app.get('/CORS-Proxy', CORS_Proxy);
app.route('/fyersToken').get(fyersToken).post(fyersToken);
app.route('/jsnb').get(javaScriptNotebook).post(javaScriptNotebook);

// Start the server.
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  // console.log(`Server is listening on port ${PORT}`);
});
