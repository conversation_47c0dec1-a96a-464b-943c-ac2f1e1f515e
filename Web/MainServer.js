const CORS_ALLOWED_ORIGINS = ['https://1nrp.github.io']; // Allowed origins for CORS.

/*
 * The secret key for signing JWTs.
 * This key is generated on server start. For production, you should use a persistent,
 * securely stored key from an environment variable.
 */
const jwtSecretKey = await crypto.subtle.generateKey(
  { name: 'HMAC', hash: 'SHA-256' },
  true, // Extractable.
  ['sign', 'verify'],
);

function base64urlEncode(buffer) {
  return btoa(String.fromCharCode(...buffer))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
}

async function CreateJWT(username) {
  const header = { alg: 'HS256', typ: 'JWT' };
  const payload = {
    sub: username,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (2 * 60 * 60), // Expires in 2 hours
  };

  const encoder = new TextEncoder();
  const encodedHeader = base64urlEncode(encoder.encode(JSON.stringify(header)));
  const encodedPayload = base64urlEncode(encoder.encode(JSON.stringify(payload)));

  const signatureData = encoder.encode(`${encodedHeader}.${encodedPayload}`);
  const signature = await crypto.subtle.sign('HMAC', jwtSecretKey, signatureData);

  const encodedSignature = base64urlEncode(new Uint8Array(signature));

  return `${encodedHeader}.${encodedPayload}.${encodedSignature}`;
}

async function VerifyJWT(token) {
  try {
    const [encodedHeader, encodedPayload, encodedSignature] = token.split('.');
    if (!encodedHeader || !encodedPayload || !encodedSignature) return null;

    const signatureData = new TextEncoder().encode(`${encodedHeader}.${encodedPayload}`);
    const signature = Uint8Array.from(atob(encodedSignature.replace(/-/g, '+').replace(/_/g, '/')), c => c.charCodeAt(0));

    const isValid = await crypto.subtle.verify('HMAC', jwtSecretKey, signature, signatureData);
    if (!isValid) return null;

    const payload = JSON.parse(new TextDecoder().decode(Uint8Array.from(atob(encodedPayload.replace(/-/g, '+').replace(/_/g, '/')), c => c.charCodeAt(0))));

    if (payload.exp < Math.floor(Date.now() / 1000)) {
      console.log('Token expired');
      return null;
    }

    return payload;
  } catch (error) {
    console.error('JWT Verification Error:', error);
    return null;
  }
}

function parseCookies(cookieHeader) {
  const cookies = {};
  if (cookieHeader) {
    cookieHeader.split(';').forEach(cookie => {
      const parts = cookie.match(/(.*?)=(.*)$/);
      if (parts) {
        const name = parts[1].trim();
        const value = parts[2].trim();
        cookies[name] = value;
      }
    });
  }
  return cookies;
}

function protectedRoute(handler) {
  return async (req) => {
    const cookies = parseCookies(req.headers.get('Cookie'));
    const token = cookies.jwt_token;

    if (!token) {
      return new Response('Unauthorized: Missing token', { status: 401 });
    }

    const payload = await VerifyJWT(token);
    if (!payload) {
      return new Response('Unauthorized: Invalid token', { status: 401 });
    }

    // Pass the request and the verified payload to the actual handler
    return handler(req, payload);
  };
}

async function ServeStaticFile(FilePath, ContentType) {
  try {
    const File = await Deno.readFile(FilePath);
    return new Response(File, {
      headers: { 'Content-Type': ContentType },
    });
  } catch (error) {
    console.error(`Error serving ${FilePath}: `, error);
    return new Response('An Error Occurred While Serving The File Request.', { status: 500 });
  }
}

async function loginHandler(req) {
  if (req.method !== 'POST') return new Response('Method Not Allowed', { status: 405 });
  try {
    const { username, password } = await req.json();
    if ( Deno.env.USERNAME === username && Deno.env.PASSWORD === password ) {
      const token = await CreateJWT(username);
      const headers = new Headers();
      headers.set('Content-Type', 'application/json');
      headers.set('Set-Cookie', `jwt_token=${token}; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=7200`);
      return new Response(JSON.stringify({ success: true, message: 'Login successful' }), { headers });
    } else {
      return new Response(JSON.stringify({ success: false, message: 'Invalid credentials' }), { status: 401, headers: { 'Content-Type': 'application/json' } });
    }
  } catch (e) {
    return new Response(JSON.stringify({ success: false, message: 'Invalid request body' }), { status: 400, headers: { 'Content-Type': 'application/json' } });
  }
}

const router = new Map();

const route = (method, pathname, handler) => router.set(`${method.toUpperCase()}:${pathname.toLowerCase()}`, handler);

route('GET', '/', () => ServeStaticFile('./index.html'));
route('GET', '/upload.js', () => ServeStaticFile('./vercelFileUploadFunctionBundled.js'));
route('POST', '/login', loginHandler); // New login route

route('POST', '/deletelink', deleteLink);
route('GET', '/getm3u8', getM3U8);
route('GET', '/getlink', getLink);
route('POST', '/savelink', saveLink);
route('GET', '/kv-and-blob-database-deletion-prevention-cron-job', cronJob);
route('POST', '/checklinkexistence', checkIfExists);
route('GET', '/tgchannels', tgChannels);
route('GET', '/cors-proxy', corsProxy);
route('GET', '/jsnb', javaScriptNotebook);
route('POST', '/jsnb', javaScriptNotebook);

route('GET', '/blob', protectedRoute(() => ServeStaticFile('./Blob.html')));
route('GET', '/fyerstoken', protectedRoute(fyersTokenHandler));
route('POST', '/fyerstoken', protectedRoute(fyersTokenHandler));
route('GET', '/getnote', protectedRoute(getNoteHandler));
route('POST', '/savenote', protectedRoute(saveNoteHandler));
route('POST', '/deletenote', protectedRoute(deleteNoteHandler));
route('GET', '/blobserver', protectedRoute(blobServerHandler));
route('POST', '/blobserver', protectedRoute(blobServerHandler));

async function mainHandler(req) {
  const url = new URL(req.url);
  const path = url.pathname;
  const method = req.method;

  // Handle CORS preflight (OPTIONS) requests
  if (method === 'OPTIONS') {
    const origin = req.headers.get('Origin');
    if (origin && CORS_ALLOWED_ORIGINS.includes(origin)) {
      return new Response(null, {
        status: 204, // No Content
        headers: {
          'Access-Control-Allow-Origin': origin,
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        },
      });
    }
    return new Response(null, { status: 204 }); // Standard response for non-matching origins.
  }

  // Find and execute the route handler
  const handler = router.get(`${method}:${path.toLowerCase()}`);
  let response;

  if (handler) {
    console.log(`Request: ${method} ${path}`);
    response = await handler(req);
  } else {
    response = new Response('Not Found', { status: 404 });
  }

  // Add CORS headers to the actual response
  const origin = req.headers.get('Origin');
  if (origin && CORS_ALLOWED_ORIGINS.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
    response.headers.set('Access-Control-Allow-Credentials', 'true');
  }

  return response;
}

console.log(`Server starting on http://localhost:${PORT}`);
Deno.serve({ port: PORT }, mainHandler);