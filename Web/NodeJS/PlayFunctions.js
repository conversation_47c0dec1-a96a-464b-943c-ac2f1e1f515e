import { put } from '@vercel/blob';

export { getLink, checkIfExists, deleteLink, saveLink, tgChannels, getM3U8 };

async function getLink(req, res) {
  try {
    const response = await fetch(`${process.env.KV_REST_API_URL}/LRANGE/TB_Links/0/-1/`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_READ_ONLY_TOKEN}`
      }
    });
    const data = await response.json();
    // Setting Cache-Control header to instruct browsers to cache responses for a period of time
    res.set('Cache-Control', 'public, max-age=10, stale-while-revalidate=172800'); // Cache for 10 seconds. Revalidate after 2 days (1,72,800 seconds).
    res.json(data);
  } catch (error) {
    console.error(error);  // Log the error for debugging
    res.status(500).json({ error: 'An error occurred while fetching from Upstash.' });
  }
}

async function checkIfExists(req, res) {
  try {
    const response = await fetch(`${process.env.KV_REST_API_URL}/LPOS/TB_Links/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Authorization': `Bearer ${process.env.KV_REST_API_READ_ONLY_TOKEN}`,
      },
      body: req.body
    });
    const data = await response.json();
    res.json(data);
    console.log('LPOS Response: ', data);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'An error occurred while checking if exists or not in Upstash.' });
  }
};

async function deleteLink(req, res) {
  try {
    const response = await fetch(`${process.env.KV_REST_API_URL}/LREM/TB_Links/0/`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`, // KV_REST_API_TOKEN}`,
      },
      body: req.body,
      method: 'POST',
    });
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'An error occurred while sending to Upstash.' });
  }
}

async function saveLink(req, res) {
  try {
    const response = await fetch(`${process.env.KV_REST_API_URL}/LPUSH/TB_Links`, {
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'Authorization': `Bearer ${process.env.KV_REST_API_TOKEN}`,
      },
      body: req.body
    });
    console.log(typeof req.body);
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'An error occurred while sending to Upstash.' });
  }
}

async function tgChannels(req, res) {
  try {
    const response = await fetch(`${process.env.KV_REST_API_URL}/LRANGE/TG_Channels/0/-1/`, {
      headers: {
        'Authorization': `Bearer ${process.env.KV_REST_API_READ_ONLY_TOKEN}`,
      }
    });
    const data = await response.json();
    // Setting Cache-Control header to instruct browsers to cache responses for a period of time
    res.set('Cache-Control', 'public, max-age=600, stale-while-revalidate=604800'); // Cache for 10 minutes (600 seconds). Revalidate after 1 week (6,04,800 seconds).
    res.json(data);
  } catch (error) {
    console.error(error);  // Log the error for debugging
    res.status(500).json({ error: 'An error occurred while fetching from Upstash.' });
  }
};


async function getM3U8(req, res) {
const { URL: shortURL } = req.query;
  try {
    const apiUrl = `https://www.terabox.com/api/shorturlinfo?app_id=250528&web=1&channel=dubox&clienttype=0&jsToken=1&dp-logid=1&shorturl=${shortURL}&root=1`;
    const infoResponse = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Referer': 'https://www.terabox.app/',
        'Origin': 'https://www.terabox.app',
        'Host': 'www.terabox.app',
        'Connection': 'keep-alive',
        'Cookie': 'csrfToken=bdWiEZYASDz8hIga_guFYm9b; browserid=ShzyQzSFYc01xXqaDMbPuKPSS7N4-R0_LV4JjH1v0NuAA-r2q2Ccc5FYhOmFQZL0WmWfjvimmGAPuXAP; lang=en; TSID=0fGynuRidat2ljUFClEwTs2vPKzgdBKt; __bid_n=196941ce69ab22816d4207; g_state={"i_l":0}; ndus=YuAqZKCteHuibgGqCA23plk9sfY9wf2z2fjSACk-; ndut_fmt=F9FE48DBC3ECF77B5A745219E8013EAA8CBEF81A61BAB6126965413C71B67BE1'
      },
    });;
    if (!infoResponse.ok) {
      throw new Error(`Failed to fetch short URL info: ${infoResponse.statusText}`);
    }

    const infoData = await infoResponse.json();
    // Extract required values.
    const { shareid, uk } = infoData;
    const { fs_id } = infoData.list[0];

    // Fetch the .m3u8 file.
    // const m3u8Url = `https://www.terabox1024.com/share/extstreaming.m3u8?uk=${uk}&shareid=${shareid}&type=M3U8_AUTO_360&fid=${fs_id}&sign=1&timestamp=1&clienttype=1&channel=1`; // Old URL format. Is not working anymore.
    const url = `https://www.terabox.app/share/streaming?uk=${uk}&shareid=${shareid}&type=M3U8_AUTO_360&fid=${fs_id}&sign=1&timestamp=1&clienttype=1&channel=1`;
    const m3url = `https://www.terabox.app/share/streaming?uk=${uk}&shareid=${shareid}&type=M3U8_FLV_264_360&fid=${fs_id}&sign=7e4b37e2b218ac98bd63752a148a22807fe03c72&timestamp=1746242771&jsToken=D2E1357A26EBAB38793124A81BF947886878332F6C0CCA28F7AB7DD0FC08A719DD4C1D0D01EFAFA09B0432264D0D1CCEACF514537EEE0078EC424E79C6FFFE8C&esl=1&isplayer=1&ehps=1&clienttype=0&app_id=250528&web=1&channel=dubox`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Referer': 'https://www.terabox.app/',
        'Origin': 'https://www.terabox.app',
        'Host': 'www.terabox.app',
        'Connection': 'keep-alive',
        'Cookie': 'csrfToken=bdWiEZYASDz8hIga_guFYm9b; browserid=ShzyQzSFYc01xXqaDMbPuKPSS7N4-R0_LV4JjH1v0NuAA-r2q2Ccc5FYhOmFQZL0WmWfjvimmGAPuXAP; lang=en; TSID=0fGynuRidat2ljUFClEwTs2vPKzgdBKt; __bid_n=196941ce69ab22816d4207; g_state={"i_l":0}; ndus=YuAqZKCteHuibgGqCA23plk9sfY9wf2z2fjSACk-; ndut_fmt=F9FE48DBC3ECF77B5A745219E8013EAA8CBEF81A61BAB6126965413C71B67BE1'
      }
    });
    
    const text = await response.text();
    const m3u8Text = text.replaceAll('v4.freeterabox.com', 'v2.4funbox.com');
    // console.log('TB Modified Response Text:', m3u8Text);
    res.set({
        "Content-Type": "application/vnd.apple.mpegurl",
        "Access-Control-Allow-Origin": "https://1nrp.github.io",
        "Cache-Control": "max-age=86400"
    });
    res.send(m3u8Text);
  } catch(error) {
    res.status(500).json({ error: "An error occurred: " + error.message });
  }
}

async function etM3U8(req, res) {
    const { URL: shortURL, AccessToken } = req.query;
  
    if (AccessToken !== "vercel-access-code-1213") {
      res.status(401).json({ message: "Wrong Access Token Code. Request Is Unauthorized" });
    }
    if (!shortURL) {
      res.status(404).send("URL parameter is required.");
    }
  
    try {
      // Fetch short URL info.
      const apiUrl = `https://www.terabox.app/api/shorturlinfo?&shorturl=${shortURL}&root=1`;
      const infoResponse = await fetch(apiUrl);

      if (!infoResponse.ok) {
        throw new Error(`Failed to fetch short URL info: ${infoResponse.statusText}`);
      }

      const infoData = await infoResponse.json();

      // Extract required values safely
      const { shareid, uk } = infoData;
      const { fs_id } = infoData.list[0];

      // Fetch the .m3u8 file
      const m3u8Url = `https://www.terabox1024.com/share/extstreaming.m3u8?uk=${uk}&shareid=${shareid}&type=M3U8_AUTO_360&fid=${fs_id}&sign=1&timestamp=1&clienttype=1&channel=1`;
      const m3u8Response = await fetch(m3u8Url, {
        method: "GET",
        headers: {
          Host: "www.terabox1024.com",
          Origin: "https://www.1024terabox.com",
          Referer: "https://www.1024terabox.com",
        },
      });

      if (!m3u8Response.ok) {
        throw new Error(`Failed to fetch M3U8 file from URL: ${m3u8Url}`);
      }

      const m3u8Content = await m3u8Response.text();
      res.set({
         "Access-Control-Allow-Origin": "https://1nrp.github.io",
         "Content-Type": "application/x-mpegURL",
         "Cache-Control": "max-age=86400"
      });
      res.send(m3u8Content);
    } catch (error) {
      console.error(error);
      res.status(500).json({ Error: `Failed to process the request: ${error.message}` });
    }
}

/*
// Fetch Terabox API.
async function getM3U8(req, res) {
  try {
    const { shortURL } = req.query;
    // Fetch data from Terabox API
    const getInfoUrl = `https://www.terabox.app/api/shorturlinfo?&shorturl=${shortURL}&root=1`;
    const response = await fetch(getInfoUrl);
    if (!response.ok) {
      throw new Error('Failed to fetch data from API.');
    }
    const infoData = await response.json();
    const { shareid, uk } = infoData;
    const { fs_id ,size, server_filename, thumbs: { url2 } } = infoData.list[0];
    // Fetch the .m3u8 file.
    const m3u8Url = `https://www.terabox1024.com/share/extstreaming.m3u8?uk=${uk}&shareid=${shareid}&type=M3U8_AUTO_360&fid=${fs_id}&sign=1&timestamp=1&clienttype=1&channel=1`;
    const m3u8Response = await fetch(m3u8Url, {
      method: 'GET',
      headers: {
        Host: 'www.terabox1024.com',
        Origin: 'https://www.1024terabox.com', 
        Referer: 'https://www.1024terabox.com/'
      }
    });
    if (!m3u8Response.ok) {
      throw new Error(`Failed to fetch M3U8 file from URL: ${m3u8Url}`);
    }
    const m3u8Content = await m3u8Response.text();
    // Save .m3u8 file to Vercel Blob storage
    const blobFileName = `M3U8-HTML/${shortURL}.m3u8`;
    //const token = process.env.BLOB_READ_WRITE_TOKEN;
    const blob = await put(blobFileName, m3u8Content, {
      access: 'public',
      addRandomSuffix: false,
      ContentType: 'application/x-mpegURL'  // Media type for .m3u8 files. 'ContentType' is a string indicating the media type. By default, it's extracted from the pathname's extension.
      //token: token,  // A string specifying the token to use when making requests. It defaults to process.env.BLOB_READ_WRITE_TOKEN when deployed on Vercel.
    });
    // Send the blob data as response.
    res.set('Cache-Control', 'public, max-age=86400, s-maxage=86400, stale-while-revalidate=172800'); // Cache for 1 day (86400 seconds). 'max-age' is for browser cache & 's-maxage' is for Vercel's Edge Network cache. Revalidate after 2 days (172800 seconds).
    res.json(blob);
  } catch (error) {
    console.error('Error:', error.message);
    res.status(500).json({ error: error.message });
  }
};
*/
/*
// Fetch Terabox 2nd API.
async function getM3U8(req, res) {
  try {
    const { shortURL } = req.query;
    // Fetching data from Terabox API.
    const getInfoUrl = `https://www.terabox.com/api/shorturlinfo?app_id=250528&web=1&channel=dubox&clienttype=0&jsToken=1&dp-logid=1&shorturl=${shortURL}&root=1`;
     const response = await fetch(getInfoUrl, {
       method: 'GET',
       referrerPolicy: 'no-referrer'
    });
    if (!response.ok) {
      throw new Error(`Failed to fetch data from ${getInfoUrl}`);
    }
    const infoData = await response.json();
    console.log(infoData);
    // Extract required values from the JSON.
    const { shareid, uk } = infoData;
    const { fs_id, size, server_filename, thumbs: { url2 } } = infoData.list[0];
    // Constructing URL for fetching .m3u8 file.
    const m3u8Url = `https://www.terabox1024.com/share/extstreaming.m3u8?uk=${uk}&shareid=${shareid}&type=M3U8_AUTO_360&fid=${fs_id}&sign=1&timestamp=1&clienttype=1&channel=1`;
    const m3u8Response = await fetch(m3u8Url, {
      method: 'GET',
      referrerPolicy: 'no-referrer'
    });
    if (!m3u8Response.ok) {
      throw new Error(`Failed to fetch .m3u8 file from URL: ${m3u8Url}`);
    }
    const m3u8Content = await m3u8Response.text();
    // Save .m3u8 file to Vercel Blob storage
    const blobFileName = `M3U8-HTML/${shortURL}.m3u8`;
    //const token = process.env.BLOB_READ_WRITE_TOKEN;
    const blob = await put(blobFileName, m3u8Content, {
      access: 'public',
      addRandomSuffix: false,
      ContentType: 'application/vnd.apple.mpegurl',  // Media type for .m3u8 files. 'ContentType' is a string indicating the media type. By default, it's extracted from the pathname's extension.
      //token: token,  // A string specifying the token to use when making requests. It defaults to process.env.BLOB_READ_WRITE_TOKEN when deployed on Vercel.
    });
    // Send the blob data as response
    res.set('Cache-Control', 'public, max-age=86400, s-maxage=172800, stale-while-revalidate=172800'); // Cache for 1 day (86400 seconds). 'max-age' is for browser cache & 's-maxage' is for Vercel's Edge Network cache. Revalidate after 2 days (172800 seconds).
    res.json(blob);
  } catch (error) {
    console.error('Error:', error.message);
    res.status(500).json({ error: error.message });
  }
};
*/
