### Single Main Server File.
All the Node.js functions have been consolidated into the "mainServer.js" function, thereby not requiring the function files to be in the "api" folder, for Vercel to recognize them as serverless functions by default. The files have been moved to the "NodeJS" folder from the "api" folder. Also, a maximum of 12 serverless function files can be added to an "api" folder, whereas with this method, there is no such limitations. This is essentially bypassing Vercel's default method of considering each javascript file in the "api" folder as a separate serverless function, by making other functions a middleware function of the "mainServer.js" function.
