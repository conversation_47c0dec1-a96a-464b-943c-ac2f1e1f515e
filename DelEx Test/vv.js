import { PlaceOrder, Socket, GetAccountBalance, GetQuotes, SetMovingAverages } from './FunctionExports.js'

//PlaceOrder({ Symbol: 'xrpusd', Quantity: 1, TradeType: 'buy', SquareOff_Only: false })
// console.log('Available Balance: ', ( await GetAccountBalance() ).slice(0, 6) )

// const DataSocket = Socket("BTCUSD"); setTimeout(() => { DataSocket.close() }, 15000)

// setInterval(CheckBreakout, 5000)

//SetMovingAverages()

(async () => {
  const KV = await Deno.openKv(), KEY = ["DENOKV", "CRED"]
  //const Creds = await KV.get(KEY) // Will Return The Credentials Object Stored In Built-In Deno KV.
  // console.log(Creds.Assets)
  const creds = JSON.parse( Deno.readTextFileSync( "./Creds.json" ) )
  await KV.set(KEY, creds)
  console.log(await KV.get(KEY))
}) // ()

let EnteredTrade = false, LoopCount = 0
async function CheckBreakout() {
  const Creds = JSON.parse( Deno.readTextFileSync( "./Creds.json" ) )
  const coins = Creds.Assets.map( (cred) => cred.Symbol ).join(',')
  const tickers = await GetQuotes( coins )
  for (const ticker of tickers) {
    const { symbol: Symbol, close: Close, quotes: { best_bid: Bid, best_ask: Ask } } = ticker
    const CurrentPrice = Close // +( (( Number(Bid) + Number(Ask) ) / 2 ).toFixed(4) ) // Use this line if you want to use the average of bid and ask price.
    // console.log('Difference in Price Of Close To Optimal Price:', +( Math.abs(Close - CurrentPrice).toFixed(4)) )
    const { High, Low } = Creds.Assets.find( (cred) => cred.Symbol === Symbol ).Details
    // console.log('CurrentPrice:', CurrentPrice, 'High:', High, 'Low:', Low)

    const BreakoutReached = LoopCount > 2 && LoopCount < 4 // CurrentPrice > High || CurrentPrice < Low
    if (BreakoutReached) {
      EnteredTrade = true
      const LONG = CurrentPrice > High
      Creds.TradeType = LONG ? "LONG" : "SHORT"
      Creds.TradedAsset = Symbol
      Creds.Quantity = Math.floor( Creds.AvailableFunds * 2 /* 10X - 1X(Buffer) Leverage */ / (CurrentPrice * Creds.LotSizes[Symbol]) )
      const { success: Success, result: { average_fill_price: EntryPrice, paid_commission: Commission } } = await PlaceOrder({ Symbol, Quantity: Creds.Quantity, TradeType: LONG ? 'buy' : 'sell', SquareOff_Only: false })
      if (Success) {
        console.log('EntryPrice:', +EntryPrice, 'Commission:', +Commission)
        const StopLoss = LONG ? Number((EntryPrice * 0.998).toFixed(4)) : +((CurrentPrice * 1.002).toFixed(4))
        // Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
        console.log(`${Creds.TradeType} Entry For ${Symbol} At ${CurrentPrice} With Stop Loss At ${StopLoss}`)
        break
      } else {
        console.log('⚠️ An Error Occurred While Placing Order:', response)
        break
      }
    }
  }
  console.log('Executed " CheckBreakout() " Function Loop ', ++LoopCount, ' Times...') // To see no. of times in console while testing. Don't use this line in production because the market is always open and no. of logs will become too much.
}

async function GenerateSignature(message) {
    const encoder = new TextEncoder();
    const keyData = encoder.encode(Deno.env.get('DELTA_API_SECRET'));
    const msgBuffer = encoder.encode(message);
  
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );
  
    const signature = await crypto.subtle.sign('HMAC', cryptoKey, msgBuffer);
    const signedHex = Array.from(new Uint8Array(signature)).map(b => b.toString(16).padStart(2, '0')).join('');
    // console.log('Signature: ', signedHex)
    return signedHex;
}
