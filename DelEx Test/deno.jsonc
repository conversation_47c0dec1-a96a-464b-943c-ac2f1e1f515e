{ // Run "deno task run" in the terminal to Execute the script.
  "imports": {
        "talib": "npm:node-talib" // JavaScript Wrapper Around TA-LIB Library Written In C/C++. 
    },
  "tasks":
    {
        "run": "deno run --allow-env --env-file='./.env' --unstable-kv --allow-read --allow-write --allow-net './Trade.js'",
        "vv": "deno run --allow-env --env-file='./.env' --unstable-kv --allow-read --allow-write --allow-net './vv.js'"
    }
}