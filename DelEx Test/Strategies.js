export { Check<PERSON>reakout, CheckStoploss, CheckMA<PERSON>rossover, CheckMAStoploss }

import { 
    GetQuotes, GetRanges, SendToTelegram,
    PlaceOrder, GetAccountBalance,
    // CreateHmac, CreateHash, Socket, GetPositions, GetOHLC}
  } from './FunctionExports.js'

const INIT_PCT_UP = 1.010, INIT_PCT_DOWN = 0.990, FINAL_PCT_UP = 1.005, FINAL_PCT_DOWN = 0.995 // Stop Loss Percentages.

// Moving Average CrossOver Strategy.
let EnteredTrade_MA = false, AchievedGoodRR_MA = false, LoopCount_MA = 0

async function CheckMACrossover() {
    const Creds = JSON.parse( Deno.readTextFileSync( './Creds.json' ) )
    const Coins = Creds.Assets.map( (cred) => cred.Symbol ).join(',')
    const Tickers = await GetQuotes( Coins )
    for (const Ticker of Tickers) {
        const { symbol: Symbol, close: Close, quotes: { best_bid: Bid, best_ask: Ask } } = Ticker
        const CurrentPrice = Close // +( (( Number(Bid) + Number(Ask) ) / 2 ).toFixed(4) ) // Use this line if you want to use the average of bid and ask price.
        const PriceArray = Creds.ClosingPrices[Symbol]
        PriceArray.shift(); PriceArray.unshift(CurrentPrice)
        const New_MA_9 = PriceArray.slice(0, 9).reduce((a, b) => a + b, 0) / 9 // New MA_9 Value.
        const New_MA_20 = PriceArray.slice(0, 20).reduce((a, b) => a + b, 0) / 20 // New MA_20 Value.
        const Position = Creds.MA_9_Positions[Symbol]
        const CrossoverReached = Position === 'Below' /* Went Up From Below */ && New_MA_9 > New_MA_20 || Position === 'Above' && New_MA_9 < New_MA_20
        if (CrossoverReached) {
            EnteredTrade_MA = true
            const LONG = Position === 'Below'
            Creds.TradeType = LONG ? "LONG" : "SHORT"
            Creds.TradedAsset = Symbol
            Creds.Quantity = Math.floor( Creds.AvailableFunds * 4.5 /* Leverage */ / (CurrentPrice * Creds.LotSizes[Symbol]) )
            const { success: Success, result: { average_fill_price: EntryPrice, paid_commission: Commission } } = await PlaceOrder({ Symbol, Quantity: Creds.Quantity, TradeType: LONG ? 'buy' : 'sell', SquareOff_Only: false })
            if (Success) {
                console.log('EntryPrice:', +EntryPrice, 'Commission:', +Commission)
                Creds.EntryPrice = EntryPrice
                Creds.StopLoss = LONG ? Number((EntryPrice * INIT_PCT_DOWN).toFixed(4)) : +((CurrentPrice * INIT_PCT_UP).toFixed(4))
                Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
                console.log(`<${Creds.TradeType}> Entry For ${+Creds.Quantity} Quantity Of ${Symbol} At ${+CurrentPrice} With Stop Loss At ${+Creds.StopLoss} Based On Moving Average Crossover.`)
                
                const message = ` <b> ${ Creds.TradeType == "LONG" ? '🟦' : '🟥' } DELTA ENTRY - ${Creds.TradeType} </b>
                <b> Coin: <i> ${Symbol} </i> </b>
                <b> Price: </b> ${CurrentPrice}
                <b> Quantity: </b> ${Creds.Quantity}
                <b> Stop Loss: </b> ${Creds.StopLoss}
                `;
                await SendToTelegram(message)
                break
            } else {
                console.log('⚠️ An Error Occurred While Placing Order:', response)
                break
            }
        }
    }
    console.log('Executed " CheckMACrossover() " Function Loop ', ++LoopCount_MA, ' Times...') // To see no. of times in console while testing. Don't use this line in production because the market is always open and no. of logs will become too much.
}

async function CheckMAStoploss() {
    const Creds = JSON.parse( Deno.readTextFileSync( './Creds.json' ) )
    const ticker = await GetQuotes( Creds.TradedAsset )
    const { close: Close, quotes: { best_bid: Bid, best_ask: Ask } } = ticker
    const CurrentPrice = Close // +( (( Number(Bid) + Number(Ask) ) / 2 ).toFixed(4) )
    const { TradeType: Type, StopLoss: Stop } = Creds
    const StopLossReached = Type === "LONG" && CurrentPrice < Stop || Type === "SHORT" && CurrentPrice > Stop
    if (!StopLossReached) {
        EnteredTrade_MA = false
        const LONG = Creds.TradeType == "LONG"
        AchievedGoodRR_MA = false
        Creds.TradeCount += 1
        const OldBalance = Creds.AvailableFunds, NewBalance = +( ( await GetAccountBalance() ).slice(0, 6) ), PnL = NewBalance - OldBalance
        await PlaceOrder({ Symbol: Creds.TradedAsset, Quantity: Creds.Quantity, TradeType: LONG ? 'sell' : 'buy', SquareOff_Only: true })
        Creds.AvailableFunds = NewBalance
        Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
        console.log(`⚠️ Exit for ${Creds.TradedAsset} at ${CurrentPrice} With P&L : $ ${+(PnL.toFixed(2))} From Moving Average Crossover Based Entry.`)
        const message = `<b> 🟨 DELTA EXIT > ${ PnL >= 0 ? " 🟢 PROFIT" : " 🔴 LOSS" } </b>
        <b> Coin: <i> ${Creds.TradedAsset} </i> </b>
        <b> Exit Price: </b> ${CurrentPrice}
        <b> P&L Amount: </b> $ ${PnL.toFixed(2)}
        <b> P&L %: </b> ${ (( PnL / OldBalance ) * 100).toFixed(2) } %
        <b> Trade Type: </b> ${Creds.TradeType}
        `;
        await SendToTelegram(message)
        await GetRanges() // Update the Price Ranges for different coins before scanning for new breakouts.
    } else {
        const { EntryPrice: Entry, StopLoss: Stop } = Creds
        if (!AchievedGoodRR_MA) AchievedGoodRR_MA = Math.abs(CurrentPrice - Entry) >= 1.5 /* 1.5 RR */ * Math.abs(Entry - Stop) // Only evaluate AchievedGoodRR (Now 1.5X) if it's not already true.

        if (Creds.TradeType === "LONG") {
            const NewStopLevel = AchievedGoodRR_MA ? CurrentPrice * FINAL_PCT_DOWN : CurrentPrice * INIT_PCT_DOWN
            Creds.StopLoss = NewStopLevel > Creds.StopLoss
                ? +(NewStopLevel.toFixed(4))
                : +Creds.StopLoss
        } else {
            const NewStopLevel = AchievedGoodRR_MA ? CurrentPrice * FINAL_PCT_UP : CurrentPrice * INIT_PCT_UP
            Creds.StopLoss = NewStopLevel < Creds.StopLoss
                ? +(NewStopLevel.toFixed(4))
                : +Creds.StopLoss
        }
        Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
    }
}

// Range Breakout Strategy.
let EnteredTrade = false, LoopCount = 0, AchievedGoodRR = false

async function CheckBreakout() {
    const Creds = JSON.parse( Deno.readTextFileSync( './Creds.json' ) )
    const coins = Creds.Assets.map( (cred) => cred.Symbol ).join(',')
    const tickers = await GetQuotes( coins )
    for (const ticker of tickers) {
        const { symbol: Symbol, close: Close, quotes: { best_bid: Bid, best_ask: Ask } } = ticker
        const CurrentPrice = Close // +( (( Number(Bid) + Number(Ask) ) / 2 ).toFixed(4) ) // Use this line if you want to use the average of bid and ask price.
        // console.log('Difference in Price Of Close To Optimal Price:', +( Math.abs(Close - CurrentPrice).toFixed(4)) )
        const { High, Low } = Creds.Assets.find( (cred) => cred.Symbol === Symbol ).Details

        const BreakoutReached = CurrentPrice > High || CurrentPrice < Low
        if (BreakoutReached) {
        EnteredTrade = true
        const LONG = CurrentPrice > High
        Creds.TradeType = LONG ? "LONG" : "SHORT"
        Creds.TradedAsset = Symbol
        Creds.Quantity = Math.floor( Creds.AvailableFunds * 4.5 /* Leverage */ / (CurrentPrice * Creds.LotSizes[Symbol]) )
        const { success: Success, result: { average_fill_price: EntryPrice, paid_commission: Commission } } = await PlaceOrder({ Symbol, Quantity: Creds.Quantity, TradeType: LONG ? 'buy' : 'sell', SquareOff_Only: false })
        if (Success) {
            console.log('EntryPrice:', +EntryPrice, 'Commission:', +Commission)
            Creds.EntryPrice = EntryPrice
            Creds.StopLoss = LONG ? Number((EntryPrice * INIT_PCT_DOWN).toFixed(4)) : +((CurrentPrice * INIT_PCT_UP).toFixed(4))
            Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
            console.log(`<${Creds.TradeType}> Entry For ${Number(Creds.Quantity)} Quantity Of ${Symbol} At ${Number(CurrentPrice)} With Stop Loss At ${+Creds.StopLoss} Based On Range Breakout.`)
            const message = ` <b> ${ Creds.TradeType == "LONG" ? '🟦' : '🟥' } DELTA ENTRY - ${Creds.TradeType} </b>
            <b> Coin: <i> ${Symbol} </i> </b>
            <b> Price: </b> ${CurrentPrice}
            <b> Quantity: </b> ${Creds.Quantity}
            <b> Stop Loss: </b> ${Creds.StopLoss}
            `;
            await SendToTelegram(message)
            break
        } else {
            console.log('⚠️ An Error Occurred While Placing Order:', response)
            break
        }
        }
      }
    console.log('Executed " CheckBreakout() " Function Loop ', ++LoopCount, ' Times...') // To see no. of times in console while testing. Don't use this line in production because the market is always open and no. of logs will become too much.
    }

    async function CheckStoploss() {
        const Creds = JSON.parse( Deno.readTextFileSync( './Creds.json' ) )
        const ticker = await GetQuotes( Creds.TradedAsset )
        const { close: Close, quotes: { best_bid: Bid, best_ask: Ask } } = ticker
        const CurrentPrice = Close // +( (( Number(Bid) + Number(Ask) ) / 2 ).toFixed(4) )
        console.log('Distance From StopLoss:', +( Math.abs(Creds.StopLoss - CurrentPrice).toFixed(4) ) )
        const StopLossReached = Creds.TradeType === "LONG" && CurrentPrice < Creds.StopLoss || Creds.TradeType === "SHORT" && CurrentPrice > Creds.StopLoss
        if (StopLossReached) {
        EnteredTrade = false
        const LONG = Creds.TradeType == "LONG"
        AchievedGoodRR = false
        Creds.TradeCount += 1
        const OldBalance = Creds.AvailableFunds, NewBalance = +( ( await GetAccountBalance() ).slice(0, 6) ), PnL = NewBalance - OldBalance
        await PlaceOrder({ Symbol: Creds.TradedAsset, Quantity: Creds.Quantity, TradeType: LONG ? 'sell' : 'buy', SquareOff_Only: true })
        Creds.AvailableFunds = NewBalance
        Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
        console.log(`⚠️ Exit for ${Creds.TradedAsset} at ${+CurrentPrice} With P&L : $ ${+(PnL.toFixed(2))} From Range Breakout Based Entry.`)
        
        const message = `<b> 🟨 DELTA EXIT > ${ PnL >= 0 ? " 🟢 PROFIT" : " 🔴 LOSS" } </b>
        <b> Coin: <i> ${Creds.TradedAsset} </i> </b>
        <b> Exit Price: </b> ${CurrentPrice}
        <b> P&L Amount: </b> $ ${PnL.toFixed(2)}
        <b> P&L %: </b> ${ (( PnL / OldBalance ) * 100).toFixed(2) } %
        <b> Trade Type: </b> ${Creds.TradeType}
        `;
        await SendToTelegram(message)
        await GetRanges() // Update the Price Ranges for different coins before scanning for new breakouts.

        } else {
        const Entry = Creds.EntryPrice , Stop = Creds.StopLoss
        // Only evaluate AchievedGoodRR if it's not already true.
        // It is coded this way to prevent making the stop loss 1% again if difference between entry and current price becomes less than difference between entry and stop loss, going forward i.e. nearing Stop Loss.
        if (!AchievedGoodRR) AchievedGoodRR = Math.abs(CurrentPrice - Entry) >= Math.abs(Entry - Stop)
        
        if (Creds.TradeType === "LONG") {
            const NewStopLevel = AchievedGoodRR ? CurrentPrice * FINAL_PCT_DOWN : CurrentPrice * INIT_PCT_DOWN
            Creds.StopLoss = NewStopLevel > Creds.StopLoss
                ? +(NewStopLevel.toFixed(4))
                : +Creds.StopLoss
        } else {
            const NewStopLevel = AchievedGoodRR ? CurrentPrice * FINAL_PCT_UP : CurrentPrice * INIT_PCT_UP
            Creds.StopLoss = NewStopLevel < Creds.StopLoss
                ? +(NewStopLevel.toFixed(4))
                : +Creds.StopLoss
        }
        Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
        }
    }